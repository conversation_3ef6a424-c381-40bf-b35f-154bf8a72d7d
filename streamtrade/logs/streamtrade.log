2025-06-27 03:13:23 - streamtrade.__main__ - INFO - <module>:262 - StreamTrade Platform - Phase 1 Testing
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - <module>:263 - Python version: 3.13.5 (main, Jun 25 2025, 02:33:16) [GCC 11.4.0]
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - <module>:264 - Project root: /home/<USER>/Learn/python/notebooks
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - <module>:265 - Histdata directory: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - run_all_tests:209 - Starting StreamTrade Phase 1 Tests
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - run_all_tests:210 - ==================================================
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Data Discovery test...
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_discovery:26 - === Testing Data Discovery ===
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_discovery:31 - Available pairs: ['AUDNZD', 'AUDUSD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPJPY', 'GBPUSD', 'NSXUSD', 'NZDUSD', 'SPXUSD', 'XAUUSD']
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDNZD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/3/DAT_MT_AUDNZD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/2/DAT_MT_AUDNZD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/1/DAT_MT_AUDNZD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/4/DAT_MT_AUDNZD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/6/DAT_MT_AUDNZD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/5/DAT_MT_AUDNZD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2024/DAT_MT_AUDNZD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDUSD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/3/DAT_MT_AUDUSD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/2/DAT_MT_AUDUSD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/1/DAT_MT_AUDUSD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/4/DAT_MT_AUDUSD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/6/DAT_MT_AUDUSD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/5/DAT_MT_AUDUSD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2024/DAT_MT_AUDUSD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_discovery:36 - EURGBP info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/3/DAT_MT_EURGBP_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/2/DAT_MT_EURGBP_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/1/DAT_MT_EURGBP_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/4/DAT_MT_EURGBP_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/6/DAT_MT_EURGBP_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/5/DAT_MT_EURGBP_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2024/DAT_MT_EURGBP_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Data Discovery test PASSED
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Data Loading test...
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_loading:47 - === Testing Data Loading ===
2025-06-27 03:13:23 - streamtrade.__main__ - INFO - test_data_loading:58 - Loading EURUSD H1 data from 2025-06-20 to 2025-06-27
2025-06-27 03:13:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:13:24 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 825 rows for EURUSD
2025-06-27 03:13:24 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 825 M1 candles to 14 H1 candles
2025-06-27 03:13:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_data_loading:69 - Successfully loaded 14 candles
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_data_loading:70 - Date range: 2025-06-20 03:00:00 to 2025-06-20 16:00:00
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_data_loading:71 - Columns: ['open', 'high', 'low', 'close', 'volume']
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_data_loading:72 - Sample data:
                        open     high      low    close  volume
datetime                                                       
2025-06-20 03:00:00  1.15150  1.15193  1.15099  1.15163       0
2025-06-20 04:00:00  1.15162  1.15266  1.15114  1.15226       0
2025-06-20 05:00:00  1.15224  1.15283  1.15194  1.15268       0
2025-06-20 06:00:00  1.15267  1.15349  1.15225  1.15240       0
2025-06-20 07:00:00  1.15240  1.15259  1.15117  1.15211       0
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_data_loading:76 - Memory usage: 148.1 MB
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Data Loading test PASSED
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Timeframe Conversion test...
2025-06-27 03:13:24 - streamtrade.__main__ - INFO - test_timeframe_conversion:90 - === Testing Timeframe Conversion ===
2025-06-27 03:13:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_loader - WARNING - load_data_range:256 - No data found for EURUSD in specified range
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - WARNING - get_data:155 - No M1 data available for EURUSD
2025-06-27 03:13:25 - streamtrade.__main__ - ERROR - test_timeframe_conversion:107 - Failed to load M1 data for conversion test
2025-06-27 03:13:25 - streamtrade.__main__ - ERROR - run_all_tests:231 - ❌ Timeframe Conversion test FAILED
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Caching test...
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_caching:137 - === Testing Caching ===
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 12000 rows for EURUSD
2025-06-27 03:13:25 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 12000 M1 candles to 201 H1 candles
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_caching:154 - First load: 0.385s
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_caching:155 - Second load: 0.000s
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_caching:156 - Cache speedup: 5063.1x
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_caching:160 - Cache stats: {'entries': 2, 'total_size_mb': np.float64(0.005218505859375), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.0003640140805925642), 'timeout_minutes': 30}
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Caching test PASSED
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Memory Management test...
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_memory_management:174 - === Testing Memory Management ===
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_memory_management:179 - Initial memory: 152.6 MB
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDNZD
2025-06-27 03:13:25 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2008 H1 candles
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDNZD
2025-06-27 03:13:25 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDNZD: 1000 candles
2025-06-27 03:13:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDUSD H1
2025-06-27 03:13:26 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDUSD
2025-06-27 03:13:26 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2014 H1 candles
2025-06-27 03:13:26 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDUSD
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDUSD: 1000 candles
2025-06-27 03:13:26 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURGBP H1
2025-06-27 03:13:26 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for EURGBP
2025-06-27 03:13:26 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2017 H1 candles
2025-06-27 03:13:26 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for EURGBP
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded EURGBP: 1000 candles
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - test_memory_management:191 - Final memory: 155.3 MB
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - test_memory_management:194 - Memory increase: 2.7 MB
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - test_memory_management:198 - Detailed memory info: {'system_memory': {'rss_mb': 155.33984375, 'vms_mb': 816.6015625, 'percent': 0.48644600714763825, 'available_mb': 14751.14453125, 'total_mb': 31933.625}, 'cache_memory': {'entries': 5, 'total_size_mb': np.float64(0.142547607421875), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.0099433319909232), 'timeout_minutes': 30}, 'total_pairs': 11}
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Memory Management test PASSED
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:238 - 
==================================================
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:239 - TEST SUMMARY
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:240 - ==================================================
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:247 - Data Discovery: PASS
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:247 - Data Loading: PASS
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:247 - Timeframe Conversion: FAIL
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:247 - Caching: PASS
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:247 - Memory Management: PASS
2025-06-27 03:13:26 - streamtrade.__main__ - INFO - run_all_tests:249 - 
Overall: 4/5 tests passed
2025-06-27 03:13:26 - streamtrade.__main__ - WARNING - run_all_tests:254 - ⚠️  1 tests failed. Please check the logs above.
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - <module>:262 - StreamTrade Platform - Phase 1 Testing
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - <module>:263 - Python version: 3.13.5 (main, Jun 25 2025, 02:33:16) [GCC 11.4.0]
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - <module>:264 - Project root: /home/<USER>/Learn/python/notebooks
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - <module>:265 - Histdata directory: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:209 - Starting StreamTrade Phase 1 Tests
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:210 - ==================================================
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Data Discovery test...
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_discovery:26 - === Testing Data Discovery ===
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_discovery:31 - Available pairs: ['AUDNZD', 'AUDUSD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPJPY', 'GBPUSD', 'NSXUSD', 'NZDUSD', 'SPXUSD', 'XAUUSD']
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDNZD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/3/DAT_MT_AUDNZD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/2/DAT_MT_AUDNZD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/1/DAT_MT_AUDNZD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/4/DAT_MT_AUDNZD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/6/DAT_MT_AUDNZD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/5/DAT_MT_AUDNZD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2024/DAT_MT_AUDNZD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDUSD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/3/DAT_MT_AUDUSD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/2/DAT_MT_AUDUSD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/1/DAT_MT_AUDUSD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/4/DAT_MT_AUDUSD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/6/DAT_MT_AUDUSD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/5/DAT_MT_AUDUSD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2024/DAT_MT_AUDUSD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_discovery:36 - EURGBP info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/3/DAT_MT_EURGBP_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/2/DAT_MT_EURGBP_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/1/DAT_MT_EURGBP_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/4/DAT_MT_EURGBP_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/6/DAT_MT_EURGBP_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/5/DAT_MT_EURGBP_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2024/DAT_MT_EURGBP_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Data Discovery test PASSED
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Data Loading test...
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:47 - === Testing Data Loading ===
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:58 - Loading EURUSD H1 data from 2025-06-20 to 2025-06-27
2025-06-27 03:14:28 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:14:28 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:28 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 824 M1 candles to 14 H1 candles
2025-06-27 03:14:28 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:69 - Successfully loaded 14 candles
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:70 - Date range: 2025-06-20 03:00:00 to 2025-06-20 16:00:00
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:71 - Columns: ['open', 'high', 'low', 'close', 'volume']
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:72 - Sample data:
                        open     high      low    close  volume
datetime                                                       
2025-06-20 03:00:00  1.15157  1.15193  1.15099  1.15163       0
2025-06-20 04:00:00  1.15162  1.15266  1.15114  1.15226       0
2025-06-20 05:00:00  1.15224  1.15283  1.15194  1.15268       0
2025-06-20 06:00:00  1.15267  1.15349  1.15225  1.15240       0
2025-06-20 07:00:00  1.15240  1.15259  1.15117  1.15211       0
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_data_loading:76 - Memory usage: 119.5 MB
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Data Loading test PASSED
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Timeframe Conversion test...
2025-06-27 03:14:28 - streamtrade.__main__ - INFO - test_timeframe_conversion:90 - === Testing Timeframe Conversion ===
2025-06-27 03:14:28 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 03:14:29 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:29 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 824 M1 candles for EURUSD
2025-06-27 03:14:29 - streamtrade.__main__ - INFO - test_timeframe_conversion:110 - Loaded 824 M1 candles for conversion test
2025-06-27 03:14:29 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M5
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:30 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 824 M1 candles to 165 M5 candles
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 165 M5 candles for EURUSD
2025-06-27 03:14:30 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M5: 165 candles
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M15
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:30 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 824 M1 candles to 55 M15 candles
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 55 M15 candles for EURUSD
2025-06-27 03:14:30 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M15: 55 candles
2025-06-27 03:14:30 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:14:31 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:31 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 824 M1 candles to 14 H1 candles
2025-06-27 03:14:31 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:14:31 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H1: 14 candles
2025-06-27 03:14:31 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H4
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 824 rows for EURUSD
2025-06-27 03:14:32 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 824 M1 candles to 5 H4 candles
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 5 H4 candles for EURUSD
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H4: 5 candles
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Timeframe Conversion test PASSED
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Caching test...
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_caching:137 - === Testing Caching ===
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 12000 rows for EURUSD
2025-06-27 03:14:32 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 12000 M1 candles to 201 H1 candles
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_caching:154 - First load: 0.393s
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_caching:155 - Second load: 0.000s
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_caching:156 - Cache speedup: 5784.2x
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_caching:160 - Cache stats: {'entries': 7, 'total_size_mb': np.float64(0.08541107177734375), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.005957803555897304), 'timeout_minutes': 30}
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Caching test PASSED
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - run_all_tests:224 - 
Running Memory Management test...
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_memory_management:174 - === Testing Memory Management ===
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_memory_management:179 - Initial memory: 131.5 MB
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDNZD
2025-06-27 03:14:32 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2008 H1 candles
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDNZD
2025-06-27 03:14:32 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDNZD: 1000 candles
2025-06-27 03:14:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDUSD H1
2025-06-27 03:14:33 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDUSD
2025-06-27 03:14:33 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2014 H1 candles
2025-06-27 03:14:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDUSD
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDUSD: 1000 candles
2025-06-27 03:14:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURGBP H1
2025-06-27 03:14:33 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for EURGBP
2025-06-27 03:14:33 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2017 H1 candles
2025-06-27 03:14:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for EURGBP
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded EURGBP: 1000 candles
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - test_memory_management:191 - Final memory: 127.9 MB
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - test_memory_management:194 - Memory increase: -3.6 MB
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - test_memory_management:198 - Detailed memory info: {'system_memory': {'rss_mb': 127.89453125, 'vms_mb': 788.8203125, 'percent': 0.4005011371242695, 'available_mb': 14751.48828125, 'total_mb': 31933.625}, 'cache_memory': {'entries': 10, 'total_size_mb': np.float64(0.22274017333984375), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.015537121466227943), 'timeout_minutes': 30}, 'total_pairs': 11}
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:229 - ✅ Memory Management test PASSED
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:238 - 
==================================================
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:239 - TEST SUMMARY
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:240 - ==================================================
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:247 - Data Discovery: PASS
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:247 - Data Loading: PASS
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:247 - Timeframe Conversion: PASS
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:247 - Caching: PASS
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:247 - Memory Management: PASS
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:249 - 
Overall: 5/5 tests passed
2025-06-27 03:14:33 - streamtrade.__main__ - INFO - run_all_tests:252 - 🎉 All tests passed! Phase 1 implementation is working correctly.
2025-06-27 03:14:50 - streamtrade.streamtrade.core.utils - WARNING - validate_dataframe:42 - Missing required columns: {'datetime'}
2025-06-27 03:14:50 - streamtrade.streamtrade.data.timeframe_converter - ERROR - convert_timeframe:105 - Input data validation failed
2025-06-27 03:14:50 - streamtrade.streamtrade.data.timeframe_converter - WARNING - convert_multiple_timeframes:275 - Failed to convert to M5
2025-06-27 03:14:51 - streamtrade.streamtrade.core.utils - WARNING - validate_dataframe:42 - Missing required columns: {'datetime'}
2025-06-27 03:14:51 - streamtrade.streamtrade.data.timeframe_converter - ERROR - convert_timeframe:105 - Input data validation failed
2025-06-27 03:14:52 - streamtrade.streamtrade.core.utils - WARNING - validate_dataframe:42 - Missing required columns: {'datetime'}
2025-06-27 03:14:52 - streamtrade.streamtrade.data.timeframe_converter - ERROR - convert_timeframe:105 - Input data validation failed
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - <module>:414 - StreamTrade Platform - Phase 1 Testing
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - <module>:415 - Python version: 3.13.5 (main, Jun 25 2025, 02:33:16) [GCC 11.4.0]
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - <module>:416 - Project root: /home/<USER>/Learn/python/notebooks
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - <module>:417 - Histdata directory: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:354 - Starting StreamTrade Phase 2 Tests
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:355 - ==================================================
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Discovery test...
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_discovery:26 - === Testing Data Discovery ===
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_discovery:31 - Available pairs: ['AUDNZD', 'AUDUSD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPJPY', 'GBPUSD', 'NSXUSD', 'NZDUSD', 'SPXUSD', 'XAUUSD']
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDNZD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/3/DAT_MT_AUDNZD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/2/DAT_MT_AUDNZD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/1/DAT_MT_AUDNZD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/4/DAT_MT_AUDNZD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/6/DAT_MT_AUDNZD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/5/DAT_MT_AUDNZD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2024/DAT_MT_AUDNZD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDUSD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/3/DAT_MT_AUDUSD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/2/DAT_MT_AUDUSD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/1/DAT_MT_AUDUSD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/4/DAT_MT_AUDUSD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/6/DAT_MT_AUDUSD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/5/DAT_MT_AUDUSD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2024/DAT_MT_AUDUSD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_discovery:36 - EURGBP info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/3/DAT_MT_EURGBP_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/2/DAT_MT_EURGBP_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/1/DAT_MT_EURGBP_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/4/DAT_MT_EURGBP_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/6/DAT_MT_EURGBP_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/5/DAT_MT_EURGBP_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2024/DAT_MT_EURGBP_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Discovery test PASSED
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Loading test...
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:47 - === Testing Data Loading ===
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:58 - Loading EURUSD H1 data from 2025-06-20 to 2025-06-27
2025-06-27 03:35:31 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:35:31 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:31 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 14 H1 candles
2025-06-27 03:35:31 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:69 - Successfully loaded 14 candles
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:70 - Date range: 2025-06-20 03:00:00 to 2025-06-20 16:00:00
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:71 - Columns: ['open', 'high', 'low', 'close', 'volume']
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:72 - Sample data:
                        open     high      low    close  volume
datetime                                                       
2025-06-20 03:00:00  1.15158  1.15178  1.15099  1.15163       0
2025-06-20 04:00:00  1.15162  1.15266  1.15114  1.15226       0
2025-06-20 05:00:00  1.15224  1.15283  1.15194  1.15268       0
2025-06-20 06:00:00  1.15267  1.15349  1.15225  1.15240       0
2025-06-20 07:00:00  1.15240  1.15259  1.15117  1.15211       0
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_data_loading:76 - Memory usage: 150.0 MB
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Loading test PASSED
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Timeframe Conversion test...
2025-06-27 03:35:31 - streamtrade.__main__ - INFO - test_timeframe_conversion:90 - === Testing Timeframe Conversion ===
2025-06-27 03:35:31 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 803 M1 candles for EURUSD
2025-06-27 03:35:32 - streamtrade.__main__ - INFO - test_timeframe_conversion:110 - Loaded 803 M1 candles for conversion test
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M5
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:32 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 161 M5 candles
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 161 M5 candles for EURUSD
2025-06-27 03:35:32 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M5: 161 candles
2025-06-27 03:35:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M15
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:33 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 54 M15 candles
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 54 M15 candles for EURUSD
2025-06-27 03:35:33 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M15: 54 candles
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:33 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 14 H1 candles
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:35:33 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H1: 14 candles
2025-06-27 03:35:33 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H4
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:34 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 5 H4 candles
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 5 H4 candles for EURUSD
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H4: 5 candles
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Timeframe Conversion test PASSED
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Caching test...
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_caching:137 - === Testing Caching ===
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 12000 rows for EURUSD
2025-06-27 03:35:34 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 12000 M1 candles to 201 H1 candles
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_caching:154 - First load: 0.355s
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_caching:155 - Second load: 0.000s
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_caching:156 - Cache speedup: 3139.0x
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_caching:160 - Cache stats: {'entries': 7, 'total_size_mb': np.float64(0.08422088623046875), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.005874782800674439), 'timeout_minutes': 30}
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Caching test PASSED
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Memory Management test...
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_memory_management:174 - === Testing Memory Management ===
2025-06-27 03:35:34 - streamtrade.__main__ - INFO - test_memory_management:179 - Initial memory: 146.1 MB
2025-06-27 03:35:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDNZD
2025-06-27 03:35:35 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2008 H1 candles
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDNZD
2025-06-27 03:35:35 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDNZD: 1000 candles
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDUSD H1
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDUSD
2025-06-27 03:35:35 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2014 H1 candles
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDUSD
2025-06-27 03:35:35 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDUSD: 1000 candles
2025-06-27 03:35:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURGBP H1
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for EURGBP
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for EURGBP
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded EURGBP: 1000 candles
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_memory_management:191 - Final memory: 160.7 MB
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_memory_management:194 - Memory increase: 14.6 MB
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_memory_management:198 - Detailed memory info: {'system_memory': {'rss_mb': 160.734375, 'vms_mb': 823.4375, 'percent': 0.5033389569771676, 'available_mb': 14557.234375, 'total_mb': 31933.625}, 'cache_memory': {'entries': 10, 'total_size_mb': np.float64(0.22154998779296875), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.015454100711005076), 'timeout_minutes': 30}, 'total_pairs': 11}
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Memory Management test PASSED
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Indicators test...
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_indicators:209 - === Testing Indicators ===
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_indicators:217 - Available indicators: ['SMA', 'EMA', 'RSI', 'MACD', 'BollingerBands', 'Stochastic', 'ATR', 'Volume']
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_indicators:221 - Created SMA indicator: SMA
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_indicators:228 - Successfully added SMA indicator to manager
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 803 rows for EURUSD
2025-06-27 03:35:36 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 803 M1 candles to 14 H1 candles
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_indicators:244 - Calculated 1 indicators successfully
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Indicators test PASSED
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Visualization test...
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:260 - === Testing Visualization ===
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:268 - ChartViewer initialized successfully
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:275 - Available pairs: 11
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:276 - Available timeframes: 9
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:277 - Available indicators: 8
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:282 - Successfully added indicator to chart viewer
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - WARNING - create_chart:226 - No data loaded for chart creation
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_visualization:287 - Successfully created empty chart
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Visualization test PASSED
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Phase 2 Integration test...
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_integration:298 - === Testing Phase 2 Integration ===
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: EURUSD H1
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 6000 rows for EURUSD
2025-06-27 03:35:36 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 6000 M1 candles to 101 H1 candles
2025-06-27 03:35:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 50 H1 candles for EURUSD
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 50 candles for EURUSD H1
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_integration:314 - Successfully loaded data in chart viewer
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: rsi_14 (RSI)
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: rsi_14 (RSI)
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: macd (MACD)
2025-06-27 03:35:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: macd (MACD)
2025-06-27 03:35:36 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 3 indicators successfully
2025-06-27 03:35:36 - streamtrade.__main__ - INFO - test_integration:328 - Added 3/3 indicators
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - test_integration:333 - Successfully created chart with data and indicators
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - test_integration:337 - Chart info: 50 candles, 3 indicators
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Phase 2 Integration test PASSED
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:386 - 
==================================================
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:387 - TEST SUMMARY
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:388 - ==================================================
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Discovery: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Loading: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Timeframe Conversion: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Caching: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Memory Management: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Indicators: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Visualization: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:395 - Phase 2 Integration: PASS
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:397 - 
Overall: 8/8 tests passed
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:400 - 🎉 All tests passed! Phase 2 implementation is working correctly.
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:401 - ✅ Data Management: Complete
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:402 - ✅ Technical Indicators: Complete
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:403 - ✅ Chart Visualization: Complete
2025-06-27 03:35:37 - streamtrade.__main__ - INFO - run_all_tests:404 - ✅ GUI Components: Ready
2025-06-27 03:38:23 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:38:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:38:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:39:00 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:39:00 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD H1
2025-06-27 03:39:00 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 03:39:00 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 60000 rows for AUDNZD
2025-06-27 03:39:00 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 60000 M1 candles to 1004 H1 candles
2025-06-27 03:39:00 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 500 H1 candles for AUDNZD
2025-06-27 03:39:00 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 500 candles for AUDNZD H1
2025-06-27 03:39:29 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:16 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:16 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:19 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:19 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:25 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:25 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:28 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:40:28 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:41:51 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:02 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:07 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:07 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:07 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M15
2025-06-27 03:42:07 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M15
2025-06-27 03:42:08 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 52535 rows for AUDNZD
2025-06-27 03:42:08 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 52535 M1 candles to 3524 M15 candles
2025-06-27 03:42:08 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 3524 M15 candles for AUDNZD
2025-06-27 03:42:08 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 3524 candles for AUDNZD M15
2025-06-27 03:42:28 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:29 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:29 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M1
2025-06-27 03:42:29 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M1
2025-06-27 03:42:30 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 10000 rows for AUDNZD
2025-06-27 03:42:30 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M1 candles for AUDNZD
2025-06-27 03:42:30 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for AUDNZD M1
2025-06-27 03:42:48 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:55 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:56 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:56 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M1
2025-06-27 03:42:56 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for AUDNZD M1
2025-06-27 03:42:58 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:58 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M15
2025-06-27 03:42:58 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M15
2025-06-27 03:42:59 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 300000 rows for AUDNZD
2025-06-27 03:42:59 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 300000 M1 candles to 20099 M15 candles
2025-06-27 03:42:59 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M15 candles for AUDNZD
2025-06-27 03:42:59 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for AUDNZD M15
2025-06-27 03:42:59 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:42:59 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:26 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:26 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:27 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:27 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M1
2025-06-27 03:43:27 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M1
2025-06-27 03:43:28 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 500 rows for AUDNZD
2025-06-27 03:43:28 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 500 M1 candles for AUDNZD
2025-06-27 03:43:28 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 500 candles for AUDNZD M1
2025-06-27 03:43:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD D1
2025-06-27 03:43:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD D1
2025-06-27 03:43:32 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 547041 rows for AUDNZD
2025-06-27 03:43:32 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 547041 M1 candles to 461 D1 candles
2025-06-27 03:43:32 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 461 D1 candles for AUDNZD
2025-06-27 03:43:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 461 candles for AUDNZD D1
2025-06-27 03:43:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:36 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD H4
2025-06-27 03:43:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H4
2025-06-27 03:43:37 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 547041 rows for AUDNZD
2025-06-27 03:43:37 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 547041 M1 candles to 2366 H4 candles
2025-06-27 03:43:37 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 2366 H4 candles for AUDNZD
2025-06-27 03:43:37 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 2366 candles for AUDNZD H4
2025-06-27 03:43:37 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:43 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:43:43 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M5
2025-06-27 03:43:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M5
2025-06-27 03:43:43 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 100000 rows for AUDNZD
2025-06-27 03:43:43 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 100000 M1 candles to 20076 M5 candles
2025-06-27 03:43:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M5 candles for AUDNZD
2025-06-27 03:43:43 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for AUDNZD M5
2025-06-27 03:43:43 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:50:14 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:50:22 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:50:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - <module>:414 - StreamTrade Platform - Phase 1 Testing
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - <module>:415 - Python version: 3.13.5 (main, Jun 25 2025, 02:33:16) [GCC 11.4.0]
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - <module>:416 - Project root: /home/<USER>/Learn/python/notebooks
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - <module>:417 - Histdata directory: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - run_all_tests:354 - Starting StreamTrade Phase 2 Tests
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - run_all_tests:355 - ==================================================
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Discovery test...
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_discovery:26 - === Testing Data Discovery ===
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_discovery:31 - Available pairs: ['AUDNZD', 'AUDUSD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPJPY', 'GBPUSD', 'NSXUSD', 'NZDUSD', 'SPXUSD', 'XAUUSD']
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDNZD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/3/DAT_MT_AUDNZD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/2/DAT_MT_AUDNZD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/1/DAT_MT_AUDNZD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/4/DAT_MT_AUDNZD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/6/DAT_MT_AUDNZD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/5/DAT_MT_AUDNZD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2024/DAT_MT_AUDNZD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDUSD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/3/DAT_MT_AUDUSD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/2/DAT_MT_AUDUSD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/1/DAT_MT_AUDUSD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/4/DAT_MT_AUDUSD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/6/DAT_MT_AUDUSD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/5/DAT_MT_AUDUSD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2024/DAT_MT_AUDUSD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_discovery:36 - EURGBP info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/3/DAT_MT_EURGBP_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/2/DAT_MT_EURGBP_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/1/DAT_MT_EURGBP_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/4/DAT_MT_EURGBP_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/6/DAT_MT_EURGBP_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/5/DAT_MT_EURGBP_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2024/DAT_MT_EURGBP_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Discovery test PASSED
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Loading test...
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_loading:47 - === Testing Data Loading ===
2025-06-27 03:56:19 - streamtrade.__main__ - INFO - test_data_loading:58 - Loading EURUSD H1 data from 2025-06-20 to 2025-06-27
2025-06-27 03:56:19 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:20 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 14 H1 candles
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_data_loading:69 - Successfully loaded 14 candles
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_data_loading:70 - Date range: 2025-06-20 03:00:00 to 2025-06-20 16:00:00
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_data_loading:71 - Columns: ['open', 'high', 'low', 'close', 'volume']
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_data_loading:72 - Sample data:
                        open     high      low    close  volume
datetime                                                       
2025-06-20 03:00:00  1.15161  1.15178  1.15153  1.15163       0
2025-06-20 04:00:00  1.15162  1.15266  1.15114  1.15226       0
2025-06-20 05:00:00  1.15224  1.15283  1.15194  1.15268       0
2025-06-20 06:00:00  1.15267  1.15349  1.15225  1.15240       0
2025-06-20 07:00:00  1.15240  1.15259  1.15117  1.15211       0
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_data_loading:76 - Memory usage: 148.4 MB
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Loading test PASSED
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Timeframe Conversion test...
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_timeframe_conversion:90 - === Testing Timeframe Conversion ===
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 782 M1 candles for EURUSD
2025-06-27 03:56:20 - streamtrade.__main__ - INFO - test_timeframe_conversion:110 - Loaded 782 M1 candles for conversion test
2025-06-27 03:56:20 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M5
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:21 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 157 M5 candles
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 157 M5 candles for EURUSD
2025-06-27 03:56:21 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M5: 157 candles
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M15
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:21 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 53 M15 candles
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 53 M15 candles for EURUSD
2025-06-27 03:56:21 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M15: 53 candles
2025-06-27 03:56:21 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:56:22 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:22 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 14 H1 candles
2025-06-27 03:56:22 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:56:22 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H1: 14 candles
2025-06-27 03:56:22 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H4
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:23 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 5 H4 candles
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 5 H4 candles for EURUSD
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H4: 5 candles
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Timeframe Conversion test PASSED
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Caching test...
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_caching:137 - === Testing Caching ===
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 12000 rows for EURUSD
2025-06-27 03:56:23 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 12000 M1 candles to 201 H1 candles
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_caching:154 - First load: 0.375s
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_caching:155 - Second load: 0.000s
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_caching:156 - Cache speedup: 2997.5x
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_caching:160 - Cache stats: {'entries': 7, 'total_size_mb': np.float64(0.06728363037109375), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.00469333359173366), 'timeout_minutes': 30}
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Caching test PASSED
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Memory Management test...
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_memory_management:174 - === Testing Memory Management ===
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_memory_management:179 - Initial memory: 161.4 MB
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDNZD
2025-06-27 03:56:23 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2008 H1 candles
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDNZD
2025-06-27 03:56:23 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDNZD: 1000 candles
2025-06-27 03:56:23 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDUSD H1
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDUSD
2025-06-27 03:56:24 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2014 H1 candles
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDUSD
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDUSD: 1000 candles
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURGBP H1
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for EURGBP
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for EURGBP
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded EURGBP: 1000 candles
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_memory_management:191 - Final memory: 157.9 MB
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_memory_management:194 - Memory increase: -3.5 MB
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_memory_management:198 - Detailed memory info: {'system_memory': {'rss_mb': 157.9140625, 'vms_mb': 820.703125, 'percent': 0.4945071613385577, 'available_mb': 13572.734375, 'total_mb': 31933.625}, 'cache_memory': {'entries': 10, 'total_size_mb': np.float64(0.20461273193359375), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.014272651502064297), 'timeout_minutes': 30}, 'total_pairs': 11}
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Memory Management test PASSED
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Indicators test...
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_indicators:209 - === Testing Indicators ===
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_indicators:217 - Available indicators: ['SMA', 'EMA', 'RSI', 'MACD', 'BollingerBands', 'Stochastic', 'ATR', 'Volume']
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_indicators:221 - Created SMA indicator: SMA
2025-06-27 03:56:24 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:56:24 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:56:24 - streamtrade.__main__ - INFO - test_indicators:228 - Successfully added SMA indicator to manager
2025-06-27 03:56:24 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:56:25 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 782 rows for EURUSD
2025-06-27 03:56:25 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 782 M1 candles to 14 H1 candles
2025-06-27 03:56:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 14 H1 candles for EURUSD
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_indicators:244 - Calculated 1 indicators successfully
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Indicators test PASSED
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Visualization test...
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:260 - === Testing Visualization ===
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:268 - ChartViewer initialized successfully
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:275 - Available pairs: 11
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:276 - Available timeframes: 9
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:277 - Available indicators: 8
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:282 - Successfully added indicator to chart viewer
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - WARNING - create_chart:226 - No data loaded for chart creation
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_visualization:287 - Successfully created empty chart
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Visualization test PASSED
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Phase 2 Integration test...
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_integration:298 - === Testing Phase 2 Integration ===
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: EURUSD H1
2025-06-27 03:56:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 03:56:25 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 6000 rows for EURUSD
2025-06-27 03:56:25 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 6000 M1 candles to 101 H1 candles
2025-06-27 03:56:25 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 50 H1 candles for EURUSD
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 50 candles for EURUSD H1
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_integration:314 - Successfully loaded data in chart viewer
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: rsi_14 (RSI)
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: rsi_14 (RSI)
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: macd (MACD)
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: macd (MACD)
2025-06-27 03:56:25 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 3 indicators successfully
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_integration:328 - Added 3/3 indicators
2025-06-27 03:56:25 - streamtrade.streamtrade.visualization.plotly_charts - ERROR - _update_layout:400 - Error updating layout: 
    Invalid value of type 'builtins.str' received for the 'selectdirection' property of layout
        Received value: 'horizontal'

    The 'selectdirection' property is an enumeration that may be specified as:
      - One of the following enumeration values:
            ['h', 'v', 'd', 'any']
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_integration:333 - Successfully created chart with data and indicators
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - test_integration:337 - Chart info: 50 candles, 3 indicators
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Phase 2 Integration test PASSED
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:386 - 
==================================================
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:387 - TEST SUMMARY
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:388 - ==================================================
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Discovery: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Loading: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Timeframe Conversion: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Caching: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Memory Management: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Indicators: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Visualization: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:395 - Phase 2 Integration: PASS
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:397 - 
Overall: 8/8 tests passed
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:400 - 🎉 All tests passed! Phase 2 implementation is working correctly.
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:401 - ✅ Data Management: Complete
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:402 - ✅ Technical Indicators: Complete
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:403 - ✅ Chart Visualization: Complete
2025-06-27 03:56:25 - streamtrade.__main__ - INFO - run_all_tests:404 - ✅ GUI Components: Ready
2025-06-27 03:57:58 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 03:57:58 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 03:57:58 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:58:11 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:58:18 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:58:18 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD H1
2025-06-27 03:58:18 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for XAUUSD H1
2025-06-27 03:58:18 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 60000 rows for XAUUSD
2025-06-27 03:58:18 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 60000 M1 candles to 1002 H1 candles
2025-06-27 03:58:18 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 500 H1 candles for XAUUSD
2025-06-27 03:58:18 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 500 candles for XAUUSD H1
2025-06-27 03:59:11 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:59:20 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:59:30 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:59:33 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:59:41 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 03:59:41 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: EMA_1 (EMA)
2025-06-27 03:59:41 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: EMA_1 (EMA)
2025-06-27 03:59:41 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 03:59:41 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:07 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:10 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:10 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:10 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: EMA_2 (EMA)
2025-06-27 04:00:10 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: EMA_2 (EMA)
2025-06-27 04:00:10 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 04:00:10 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:24 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:24 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 04:00:24 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 04:00:24 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:30 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:00:30 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:07 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:15 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:15 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:25 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:30 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:34 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: BollingerBands_1 (BollingerBands)
2025-06-27 04:04:34 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: BollingerBands_1 (BollingerBands)
2025-06-27 04:04:34 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 3 indicators successfully
2025-06-27 04:04:35 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:38 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:40 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:42 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:42 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: Stochastic_1 (Stochastic)
2025-06-27 04:04:42 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: Stochastic_1 (Stochastic)
2025-06-27 04:04:42 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 4 indicators successfully
2025-06-27 04:04:42 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:45 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:04:46 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:06:49 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:06:49 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:06:52 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 04:06:52 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 04:06:52 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 04:06:53 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 04:06:53 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 04:06:53 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 04:06:53 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 04:06:54 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 04:06:55 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 04:06:55 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 04:06:55 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 04:06:55 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 04:06:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 04:06:57 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 04:06:57 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 04:06:57 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 04:06:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:06:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - <module>:414 - StreamTrade Platform - Phase 1 Testing
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - <module>:415 - Python version: 3.13.5 (main, Jun 25 2025, 02:33:16) [GCC 11.4.0]
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - <module>:416 - Project root: /home/<USER>/Learn/python/notebooks
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - <module>:417 - Histdata directory: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:354 - Starting StreamTrade Phase 2 Tests
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:355 - ==================================================
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Discovery test...
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_discovery:26 - === Testing Data Discovery ===
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_discovery:31 - Available pairs: ['AUDNZD', 'AUDUSD', 'EURGBP', 'EURJPY', 'EURUSD', 'GBPJPY', 'GBPUSD', 'NSXUSD', 'NZDUSD', 'SPXUSD', 'XAUUSD']
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDNZD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/3/DAT_MT_AUDNZD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/2/DAT_MT_AUDNZD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/1/DAT_MT_AUDNZD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/4/DAT_MT_AUDNZD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/6/DAT_MT_AUDNZD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2025/5/DAT_MT_AUDNZD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audnzd/2024/DAT_MT_AUDNZD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_discovery:36 - AUDUSD info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/3/DAT_MT_AUDUSD_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/2/DAT_MT_AUDUSD_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/1/DAT_MT_AUDUSD_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/4/DAT_MT_AUDUSD_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/6/DAT_MT_AUDUSD_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2025/5/DAT_MT_AUDUSD_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/audusd/2024/DAT_MT_AUDUSD_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_discovery:36 - EURGBP info: {'date_range': (Timestamp('2024-01-01 17:04:00'), Timestamp('2025-06-20 16:58:00')), 'files_by_year': {'2025': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/3/DAT_MT_EURGBP_M1_202503.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/2/DAT_MT_EURGBP_M1_202502.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/1/DAT_MT_EURGBP_M1_202501.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/4/DAT_MT_EURGBP_M1_202504.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/6/DAT_MT_EURGBP_M1_202506.csv'), PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2025/5/DAT_MT_EURGBP_M1_202505.csv')], '2024': [PosixPath('/home/<USER>/Learn/python/notebooks/histdata/MT/M1/eurgbp/2024/DAT_MT_EURGBP_M1_2024.csv')]}, 'total_files': 7}
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Discovery test PASSED
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Data Loading test...
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:47 - === Testing Data Loading ===
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:58 - Loading EURUSD H1 data from 2025-06-20 to 2025-06-27
2025-06-27 04:11:41 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 04:11:41 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:41 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 767 M1 candles to 13 H1 candles
2025-06-27 04:11:41 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 13 H1 candles for EURUSD
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:69 - Successfully loaded 13 candles
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:70 - Date range: 2025-06-20 04:00:00 to 2025-06-20 16:00:00
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:71 - Columns: ['open', 'high', 'low', 'close', 'volume']
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:72 - Sample data:
                        open     high      low    close  volume
datetime                                                       
2025-06-20 04:00:00  1.15142  1.15266  1.15114  1.15226       0
2025-06-20 05:00:00  1.15224  1.15283  1.15194  1.15268       0
2025-06-20 06:00:00  1.15267  1.15349  1.15225  1.15240       0
2025-06-20 07:00:00  1.15240  1.15259  1.15117  1.15211       0
2025-06-20 08:00:00  1.15210  1.15267  1.15055  1.15125       0
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_data_loading:76 - Memory usage: 149.6 MB
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Data Loading test PASSED
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Timeframe Conversion test...
2025-06-27 04:11:41 - streamtrade.__main__ - INFO - test_timeframe_conversion:90 - === Testing Timeframe Conversion ===
2025-06-27 04:11:41 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 767 M1 candles for EURUSD
2025-06-27 04:11:42 - streamtrade.__main__ - INFO - test_timeframe_conversion:110 - Loaded 767 M1 candles for conversion test
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M5
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:42 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 767 M1 candles to 154 M5 candles
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 154 M5 candles for EURUSD
2025-06-27 04:11:42 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M5: 154 candles
2025-06-27 04:11:42 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M15
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:43 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 767 M1 candles to 52 M15 candles
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 52 M15 candles for EURUSD
2025-06-27 04:11:43 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - M15: 52 candles
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:43 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 767 M1 candles to 13 H1 candles
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 13 H1 candles for EURUSD
2025-06-27 04:11:43 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H1: 13 candles
2025-06-27 04:11:43 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H4
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:44 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 767 M1 candles to 4 H4 candles
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 4 H4 candles for EURUSD
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_timeframe_conversion:124 - H4: 4 candles
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Timeframe Conversion test PASSED
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Caching test...
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_caching:137 - === Testing Caching ===
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 12000 rows for EURUSD
2025-06-27 04:11:44 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 12000 M1 candles to 201 H1 candles
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_caching:154 - First load: 0.351s
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_caching:155 - Second load: 0.000s
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_caching:156 - Cache speedup: 3618.5x
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_caching:160 - Cache stats: {'entries': 7, 'total_size_mb': np.float64(0.06627655029296875), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.004623085260391235), 'timeout_minutes': 30}
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Caching test PASSED
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Memory Management test...
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_memory_management:174 - === Testing Memory Management ===
2025-06-27 04:11:44 - streamtrade.__main__ - INFO - test_memory_management:179 - Initial memory: 161.9 MB
2025-06-27 04:11:44 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDNZD
2025-06-27 04:11:45 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2008 H1 candles
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDNZD
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDNZD: 1000 candles
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDUSD H1
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for AUDUSD
2025-06-27 04:11:45 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2014 H1 candles
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for AUDUSD
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded AUDUSD: 1000 candles
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURGBP H1
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 120000 rows for EURGBP
2025-06-27 04:11:45 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 120000 M1 candles to 2017 H1 candles
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 1000 H1 candles for EURGBP
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:187 - Loaded EURGBP: 1000 candles
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:191 - Final memory: 159.5 MB
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:194 - Memory increase: -2.5 MB
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_memory_management:198 - Detailed memory info: {'system_memory': {'rss_mb': 159.484375, 'vms_mb': 821.17578125, 'percent': 0.49942458771905796, 'available_mb': 14422.375, 'total_mb': 31933.625}, 'cache_memory': {'entries': 10, 'total_size_mb': np.float64(0.20360565185546875), 'max_size_mb': 1433.6, 'usage_percent': np.float64(0.014202403170721873), 'timeout_minutes': 30}, 'total_pairs': 11}
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Memory Management test PASSED
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Indicators test...
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_indicators:209 - === Testing Indicators ===
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_indicators:217 - Available indicators: ['SMA', 'EMA', 'RSI', 'MACD', 'BollingerBands', 'Stochastic', 'ATR', 'Volume']
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_indicators:221 - Created SMA indicator: SMA
2025-06-27 04:11:45 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:11:45 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 04:11:45 - streamtrade.__main__ - INFO - test_indicators:228 - Successfully added SMA indicator to manager
2025-06-27 04:11:45 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 04:11:46 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 767 rows for EURUSD
2025-06-27 04:11:46 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 100 H1 candles for EURUSD
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_indicators:244 - Calculated 1 indicators successfully
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Indicators test PASSED
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Visualization test...
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:260 - === Testing Visualization ===
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:268 - ChartViewer initialized successfully
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:275 - Available pairs: 11
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:276 - Available timeframes: 9
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:277 - Available indicators: 8
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:282 - Successfully added indicator to chart viewer
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - WARNING - create_chart:228 - No data loaded for chart creation
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_visualization:287 - Successfully created empty chart
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Visualization test PASSED
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:372 - 
Running Phase 2 Integration test...
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_integration:298 - === Testing Phase 2 Integration ===
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: EURUSD H1
2025-06-27 04:11:46 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD H1
2025-06-27 04:11:46 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 6000 rows for EURUSD
2025-06-27 04:11:46 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 6000 M1 candles to 101 H1 candles
2025-06-27 04:11:46 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 50 H1 candles for EURUSD
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 50 candles for EURUSD H1
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_integration:314 - Successfully loaded data in chart viewer
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: sma_20 (SMA)
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: sma_20 (SMA)
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: rsi_14 (RSI)
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: rsi_14 (RSI)
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 2 indicators successfully
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: macd (MACD)
2025-06-27 04:11:46 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: macd (MACD)
2025-06-27 04:11:46 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 3 indicators successfully
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_integration:328 - Added 3/3 indicators
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_integration:333 - Successfully created chart with data and indicators
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - test_integration:337 - Chart info: 50 candles, 3 indicators
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:377 - ✅ Phase 2 Integration test PASSED
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:386 - 
==================================================
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:387 - TEST SUMMARY
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:388 - ==================================================
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Discovery: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Data Loading: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Timeframe Conversion: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Caching: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Memory Management: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Indicators: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Visualization: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:395 - Phase 2 Integration: PASS
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:397 - 
Overall: 8/8 tests passed
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:400 - 🎉 All tests passed! Phase 2 implementation is working correctly.
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:401 - ✅ Data Management: Complete
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:402 - ✅ Technical Indicators: Complete
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:403 - ✅ Chart Visualization: Complete
2025-06-27 04:11:46 - streamtrade.__main__ - INFO - run_all_tests:404 - ✅ GUI Components: Ready
2025-06-27 04:12:20 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:12:20 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:12:20 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:12:31 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:12:33 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:12:35 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:12:36 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:12:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: EURUSD M1
2025-06-27 04:12:36 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for EURUSD M1
2025-06-27 04:12:37 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 10000 rows for EURUSD
2025-06-27 04:12:37 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M1 candles for EURUSD
2025-06-27 04:12:37 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for EURUSD M1
2025-06-27 04:14:27 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:14:27 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:15:47 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:15:49 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:15:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:02 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:06 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:06 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:11 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:12 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:16:12 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:16 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:28 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:34 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: ATR_1 (ATR)
2025-06-27 04:18:34 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: ATR_1 (ATR)
2025-06-27 04:18:34 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 04:18:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:36 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:37 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:56 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:18:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:14 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:20 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:20 - streamtrade.streamtrade.indicators.indicator_manager - INFO - remove_indicator:133 - Removed indicator: ATR_1
2025-06-27 04:19:20 - streamtrade.streamtrade.visualization.chart_viewer - INFO - remove_indicator:150 - Removed indicator: ATR_1
2025-06-27 04:19:20 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:39 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:39 - streamtrade.streamtrade.indicators.indicator_manager - INFO - add_indicator:104 - Added indicator: Volume_1 (Volume)
2025-06-27 04:19:39 - streamtrade.streamtrade.visualization.chart_viewer - INFO - add_indicator:129 - Added indicator: Volume_1 (Volume)
2025-06-27 04:19:39 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 04:19:39 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:43 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:19:43 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:20:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:20:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: EURUSD M1
2025-06-27 04:20:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for EURUSD M1
2025-06-27 04:20:23 - streamtrade.streamtrade.indicators.indicator_manager - INFO - calculate_all:232 - Calculated 1 indicators successfully
2025-06-27 04:20:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:20:50 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:20:50 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:20:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:04 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:12 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:14 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:25 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:37 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:21:37 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD M1
2025-06-27 04:21:37 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD M1
2025-06-27 04:21:37 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 10000 rows for AUDNZD
2025-06-27 04:21:37 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M1 candles for AUDNZD
2025-06-27 04:21:37 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for AUDNZD M1
2025-06-27 04:43:56 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:43:56 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:43:56 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:45:23 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 04:45:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 04:45:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:45:42 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:45:52 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:46:02 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:46:02 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD H1
2025-06-27 04:46:02 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 04:46:02 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 60000 rows for AUDNZD
2025-06-27 04:46:02 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 60000 M1 candles to 1004 H1 candles
2025-06-27 04:46:02 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 500 H1 candles for AUDNZD
2025-06-27 04:46:02 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 500 candles for AUDNZD H1
2025-06-27 04:55:21 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:55:48 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:55:53 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:55:54 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:55:55 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:55:55 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:00 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:00 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:06 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:06 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:06 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:57:09 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD H1
2025-06-27 04:57:09 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD H1
2025-06-27 04:57:09 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 547041 rows for AUDNZD
2025-06-27 04:57:09 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 547041 M1 candles to 9171 H1 candles
2025-06-27 04:57:09 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 9171 H1 candles for AUDNZD
2025-06-27 04:57:09 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 9171 candles for AUDNZD H1
2025-06-27 04:57:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:21 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:21 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD D1
2025-06-27 04:59:21 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for AUDNZD D1
2025-06-27 04:59:22 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 547041 rows for AUDNZD
2025-06-27 04:59:22 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 547041 M1 candles to 461 D1 candles
2025-06-27 04:59:22 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 461 D1 candles for AUDNZD
2025-06-27 04:59:22 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 461 candles for AUDNZD D1
2025-06-27 04:59:22 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD D1
2025-06-27 04:59:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 461 candles for AUDNZD D1
2025-06-27 04:59:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:36 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 04:59:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: AUDNZD D1
2025-06-27 04:59:36 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 461 candles for AUDNZD D1
2025-06-27 04:59:36 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:15 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:20 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:26 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:28 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:28 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD H1
2025-06-27 05:00:28 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for XAUUSD H1
2025-06-27 05:00:29 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 50394 rows for XAUUSD
2025-06-27 05:00:29 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 50394 M1 candles to 841 H1 candles
2025-06-27 05:00:29 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 841 H1 candles for XAUUSD
2025-06-27 05:00:29 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 841 candles for XAUUSD H1
2025-06-27 05:00:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD H1
2025-06-27 05:00:32 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 841 candles for XAUUSD H1
2025-06-27 05:00:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:34 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD H1
2025-06-27 05:00:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for XAUUSD H1
2025-06-27 05:00:35 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 521840 rows for XAUUSD
2025-06-27 05:00:35 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 521840 M1 candles to 8707 H1 candles
2025-06-27 05:00:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 8707 H1 candles for XAUUSD
2025-06-27 05:00:35 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 8707 candles for XAUUSD H1
2025-06-27 05:00:35 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:35 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:47 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:47 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD H1
2025-06-27 05:00:47 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 841 candles for XAUUSD H1
2025-06-27 05:00:47 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:51 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:53 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:00:53 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD M1
2025-06-27 05:00:53 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for XAUUSD M1
2025-06-27 05:00:54 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 10000 rows for XAUUSD
2025-06-27 05:00:54 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M1 candles for XAUUSD
2025-06-27 05:00:54 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for XAUUSD M1
2025-06-27 05:01:05 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:05 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD M5
2025-06-27 05:01:05 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for XAUUSD M5
2025-06-27 05:01:05 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 100000 rows for XAUUSD
2025-06-27 05:01:05 - streamtrade.streamtrade.data.timeframe_converter - INFO - convert_timeframe:143 - Successfully converted 100000 M1 candles to 20006 M5 candles
2025-06-27 05:01:05 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M5 candles for XAUUSD
2025-06-27 05:01:05 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for XAUUSD M5
2025-06-27 05:01:05 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:05 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:09 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD M5
2025-06-27 05:01:09 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for XAUUSD M5
2025-06-27 05:01:09 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:23 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:01:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD M5
2025-06-27 05:01:23 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for XAUUSD M5
2025-06-27 05:01:24 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:02:14 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:02:14 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:02:14 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:02:14 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:14 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:02:15 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:02:17 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:02:18 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:18 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:02:18 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:02:18 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:02:18 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:02:30 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:02:30 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:02:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:02:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:02:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:02:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:32 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:02:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:02:32 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:33 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:02:34 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:02:35 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:02:35 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:02:35 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:02:35 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:05:43 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:05:43 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:05:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:05:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:05:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:05:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:05:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:05:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:05:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:05:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:05:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:05:47 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:05:47 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:05:47 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:05:47 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:05:47 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:05:47 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:06:28 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:06:28 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:06:28 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:06:29 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:29 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:06:29 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:29 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:06:30 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:06:30 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:06:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:06:32 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:06:33 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:33 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:06:33 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:06:33 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:06:41 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:06:41 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:06:41 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:06:42 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:42 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:06:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:06:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:06:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:06:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:06:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:06:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:06:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:06:46 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:06:46 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:06:46 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:11:42 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:11:42 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:11:42 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:11:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:11:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:11:43 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:11:43 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:11:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:11:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:11:44 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:11:44 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:11:45 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:11:46 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:11:47 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:11:47 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:11:47 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:11:47 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:13:27 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:13:27 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:13:27 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:13:28 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:28 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:13:28 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:28 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:13:29 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:29 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:13:29 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:13:29 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:30 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:31 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:13:32 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:32 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:13:32 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:13:32 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:13:37 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:13:37 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:13:37 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:13:38 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:38 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:13:38 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:38 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:13:39 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:39 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:13:39 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:13:39 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:40 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:41 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:13:42 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:42 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:13:42 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:13:42 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:13:53 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:13:53 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:13:53 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:13:53 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:53 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:13:54 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:54 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:13:54 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:54 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:13:55 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:13:55 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:13:55 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:55 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:56 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:13:57 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:13:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:00 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:14:00 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:14:00 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:14:01 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:01 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:14:02 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:03 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:04 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:14:05 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:05 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:14:05 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:14:05 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:07 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:14:07 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:14:07 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:14:08 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:08 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:14:08 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:08 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:14:09 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:09 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:14:09 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:14:09 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:10 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:11 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:14:12 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:12 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:14:12 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:14:12 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:14 - streamtrade.streamtrade.data.data_loader - INFO - __init__:43 - DataLoader initialized with histdata_dir: /home/<USER>/Learn/python/notebooks/histdata/MT/M1
2025-06-27 05:14:14 - streamtrade.streamtrade.data.timeframe_converter - INFO - __init__:45 - TimeframeConverter initialized with 9 timeframes
2025-06-27 05:14:14 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDNZD across 2 years
2025-06-27 05:14:15 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDNZD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:15 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for AUDUSD across 2 years
2025-06-27 05:14:15 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for AUDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:15 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURGBP across 2 years
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURGBP: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURJPY across 2 years
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURJPY: 2024-01-01 17:06:00 to 2025-06-20 16:58:00
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for EURUSD across 2 years
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for EURUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:16 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPJPY across 2 years
2025-06-27 05:14:17 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPJPY: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:17 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for GBPUSD across 2 years
2025-06-27 05:14:17 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for GBPUSD: 2024-01-01 17:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:17 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NSXUSD across 2 years
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NSXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for NZDUSD across 2 years
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for NZDUSD: 2024-01-01 17:04:00 to 2025-06-20 16:58:00
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for SPXUSD across 2 years
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for SPXUSD: 2024-01-01 18:00:00 to 2025-06-20 16:13:00
2025-06-27 05:14:18 - streamtrade.streamtrade.data.data_loader - INFO - discover_files:103 - Discovered 7 files for XAUUSD across 2 years
2025-06-27 05:14:19 - streamtrade.streamtrade.data.data_loader - INFO - get_available_date_range:324 - Available date range for XAUUSD: 2024-01-01 18:00:00 to 2025-06-20 16:58:00
2025-06-27 05:14:19 - streamtrade.streamtrade.data.data_manager - INFO - _refresh_pairs_info:65 - Refreshed info for 11 pairs
2025-06-27 05:14:19 - streamtrade.streamtrade.data.data_manager - INFO - __init__:50 - DataManager initialized successfully
2025-06-27 05:14:19 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:37 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:49 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:50 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: XAUUSD M5
2025-06-27 05:14:50 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for XAUUSD M5
2025-06-27 05:14:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:14:50 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:15:42 - streamtrade.streamtrade.indicators.indicator_manager - INFO - __init__:44 - IndicatorManager initialized
2025-06-27 05:15:42 - streamtrade.streamtrade.visualization.chart_viewer - INFO - __init__:43 - ChartViewer initialized
2025-06-27 05:15:42 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:16:27 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:16:29 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:16:31 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:16:34 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:16:34 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: GBPUSD M1
2025-06-27 05:16:34 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for GBPUSD M1
2025-06-27 05:16:35 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 10000 rows for GBPUSD
2025-06-27 05:16:35 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 10000 M1 candles for GBPUSD
2025-06-27 05:16:35 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 10000 candles for GBPUSD M1
2025-06-27 05:16:57 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:17:02 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:17:02 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:17:02 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: GBPUSD M1
2025-06-27 05:17:02 - streamtrade.streamtrade.data.data_manager - INFO - get_data:136 - Loading data for GBPUSD M1
2025-06-27 05:17:03 - streamtrade.streamtrade.data.data_loader - INFO - load_data_range:272 - Successfully loaded 250 rows for GBPUSD
2025-06-27 05:17:03 - streamtrade.streamtrade.data.data_manager - INFO - get_data:181 - Successfully loaded 250 M1 candles for GBPUSD
2025-06-27 05:17:03 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 250 candles for GBPUSD M1
2025-06-27 05:17:26 - streamtrade.streamtrade.gui.main_app - INFO - initialize_components:108 - StreamTrade app components initialized successfully
2025-06-27 05:17:26 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:67 - Loading data: GBPUSD M1
2025-06-27 05:17:26 - streamtrade.streamtrade.visualization.chart_viewer - INFO - load_data:94 - Loaded 250 candles for GBPUSD M1
