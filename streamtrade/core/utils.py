"""
Core utility functions for StreamTrade platform.
"""

import os
import psutil
import pandas as pd
import numpy as np
from typing import Optional, Union, Tuple, Any
from pathlib import Path
from datetime import datetime, timedelta
import pytz

from ..config.logging_config import get_logger

logger = get_logger(__name__)


def validate_dataframe(df: pd.DataFrame, required_columns: list = None) -> bool:
    """
    Validate if DataFrame has required structure for OHLCV data.
    
    Args:
        df: DataFrame to validate
        required_columns: List of required column names
        
    Returns:
        True if valid, False otherwise
    """
    if required_columns is None:
        required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
    
    try:
        # Check if DataFrame is not empty
        if df.empty:
            logger.warning("DataFrame is empty")
            return False
        
        # Check required columns
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            logger.warning(f"Missing required columns: {missing_columns}")
            return False
        
        # Check data types
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
                logger.warning(f"Column {col} is not numeric")
                return False
        
        # Check for datetime column
        if 'datetime' in df.columns:
            if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
                logger.warning("datetime column is not datetime type")
                return False
        
        # Check OHLC logic (High >= Low, Open/Close between High/Low)
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            invalid_ohlc = (
                (df['high'] < df['low']) |
                (df['open'] > df['high']) | (df['open'] < df['low']) |
                (df['close'] > df['high']) | (df['close'] < df['low'])
            )
            
            if invalid_ohlc.any():
                logger.warning(f"Found {invalid_ohlc.sum()} rows with invalid OHLC data")
                return False
        
        logger.debug(f"DataFrame validation passed: {len(df)} rows, {len(df.columns)} columns")
        return True
        
    except Exception as e:
        logger.error(f"Error validating DataFrame: {str(e)}")
        return False


def memory_usage() -> dict:
    """
    Get current memory usage information.
    
    Returns:
        Dictionary with memory usage statistics
    """
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'total_mb': psutil.virtual_memory().total / 1024 / 1024
        }
    except Exception as e:
        logger.error(f"Error getting memory usage: {str(e)}")
        return {}


def format_number(
    value: Union[int, float], 
    precision: int = 2, 
    use_thousands_separator: bool = True
) -> str:
    """
    Format number for display with specified precision.
    
    Args:
        value: Number to format
        precision: Decimal places
        use_thousands_separator: Whether to use comma separator
        
    Returns:
        Formatted string
    """
    try:
        if pd.isna(value) or value is None:
            return "N/A"
        
        if use_thousands_separator:
            return f"{value:,.{precision}f}"
        else:
            return f"{value:.{precision}f}"
            
    except (ValueError, TypeError):
        return str(value)


def calculate_file_size(filepath: Union[str, Path]) -> dict:
    """
    Calculate file size and return in different units.
    
    Args:
        filepath: Path to file
        
    Returns:
        Dictionary with size in different units
    """
    try:
        size_bytes = os.path.getsize(filepath)
        
        return {
            'bytes': size_bytes,
            'kb': size_bytes / 1024,
            'mb': size_bytes / 1024 / 1024,
            'gb': size_bytes / 1024 / 1024 / 1024,
            'formatted': format_file_size(size_bytes)
        }
    except Exception as e:
        logger.error(f"Error calculating file size for {filepath}: {str(e)}")
        return {}


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 ** 2:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 ** 3:
        return f"{size_bytes / 1024 ** 2:.1f} MB"
    else:
        return f"{size_bytes / 1024 ** 3:.1f} GB"


def safe_division(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Perform safe division avoiding division by zero.
    
    Args:
        numerator: Numerator value
        denominator: Denominator value
        default: Default value if division by zero
        
    Returns:
        Division result or default value
    """
    try:
        if denominator == 0 or pd.isna(denominator):
            return default
        return numerator / denominator
    except (TypeError, ValueError):
        return default


def convert_timezone(
    dt: datetime, 
    from_tz: str = 'EST', 
    to_tz: str = 'UTC'
) -> datetime:
    """
    Convert datetime from one timezone to another.
    
    Args:
        dt: Datetime object
        from_tz: Source timezone
        to_tz: Target timezone
        
    Returns:
        Converted datetime
    """
    try:
        # Handle EST without DST (as per histdata specification)
        if from_tz == 'EST':
            from_timezone = pytz.timezone('US/Eastern')
            # Force EST (no DST)
            dt_localized = from_timezone.localize(dt, is_dst=False)
        else:
            from_timezone = pytz.timezone(from_tz)
            dt_localized = from_timezone.localize(dt)
        
        to_timezone = pytz.timezone(to_tz)
        return dt_localized.astimezone(to_timezone)
        
    except Exception as e:
        logger.error(f"Error converting timezone: {str(e)}")
        return dt


def create_datetime_index(
    start_date: datetime,
    end_date: datetime,
    timeframe: str = 'H1'
) -> pd.DatetimeIndex:
    """
    Create datetime index for given timeframe.
    
    Args:
        start_date: Start datetime
        end_date: End datetime
        timeframe: Timeframe (M1, M5, H1, etc.)
        
    Returns:
        DatetimeIndex
    """
    try:
        # Convert timeframe to pandas frequency
        freq_map = {
            'M1': '1T',   # 1 minute
            'M5': '5T',   # 5 minutes
            'M15': '15T', # 15 minutes
            'M30': '30T', # 30 minutes
            'H1': '1H',   # 1 hour
            'H4': '4H',   # 4 hours
            'D1': '1D',   # 1 day
            'W1': '1W',   # 1 week
            'MN1': '1M'   # 1 month
        }
        
        freq = freq_map.get(timeframe, '1H')
        return pd.date_range(start=start_date, end=end_date, freq=freq)
        
    except Exception as e:
        logger.error(f"Error creating datetime index: {str(e)}")
        return pd.DatetimeIndex([])


def chunk_dataframe(df: pd.DataFrame, chunk_size: int = 10000):
    """
    Generator to yield DataFrame chunks.
    
    Args:
        df: DataFrame to chunk
        chunk_size: Size of each chunk
        
    Yields:
        DataFrame chunks
    """
    for i in range(0, len(df), chunk_size):
        yield df.iloc[i:i + chunk_size]


def estimate_memory_usage(df: pd.DataFrame) -> dict:
    """
    Estimate memory usage of DataFrame.
    
    Args:
        df: DataFrame to analyze
        
    Returns:
        Memory usage statistics
    """
    try:
        memory_usage = df.memory_usage(deep=True)
        total_mb = memory_usage.sum() / 1024 / 1024
        
        return {
            'total_mb': total_mb,
            'per_column': {col: usage / 1024 / 1024 
                          for col, usage in memory_usage.items()},
            'rows': len(df),
            'columns': len(df.columns),
            'mb_per_row': total_mb / len(df) if len(df) > 0 else 0
        }
    except Exception as e:
        logger.error(f"Error estimating memory usage: {str(e)}")
        return {}
