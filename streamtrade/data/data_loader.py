"""
Data loader for handling CSV files from histdata.com format.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, List, Dict, Union, Tuple
from datetime import datetime, timedelta
import glob
import os

from ..config.settings import settings
from ..config.logging_config import get_logger, log_performance
from ..core.utils import validate_dataframe, calculate_file_size, memory_usage

logger = get_logger(__name__)


class DataLoader:
    """
    Handles loading and parsing of histdata.com CSV files.
    
    Features:
    - Lazy loading for memory efficiency
    - Chunked reading for large files
    - Data validation and cleaning
    - Automatic file discovery
    - Caching support
    """
    
    def __init__(self):
        self.histdata_dir = settings.histdata_dir
        self.chunk_size = settings.data_settings["chunk_size"]
        self.timezone = settings.data_settings["timezone"]
        self.date_format = settings.data_settings["date_format"]
        self.time_format = settings.data_settings["time_format"]
        
        # Cache for file listings
        self._file_cache = {}
        self._data_cache = {}
        
        logger.info(f"DataLoader initialized with histdata_dir: {self.histdata_dir}")
    
    def discover_files(self, pair: str, force_refresh: bool = False) -> Dict[str, List[Path]]:
        """
        Discover all available data files for a currency pair.
        
        Args:
            pair: Currency pair (e.g., 'EURUSD')
            force_refresh: Force refresh of file cache
            
        Returns:
            Dictionary with years as keys and list of file paths as values
        """
        pair_lower = pair.lower()
        cache_key = f"files_{pair_lower}"
        
        if not force_refresh and cache_key in self._file_cache:
            return self._file_cache[cache_key]
        
        pair_dir = self.histdata_dir / pair_lower
        files_by_year = {}
        
        if not pair_dir.exists():
            logger.warning(f"Pair directory not found: {pair_dir}")
            return files_by_year
        
        try:
            # Scan for files in year directories
            for year_dir in pair_dir.iterdir():
                if not year_dir.is_dir():
                    continue
                
                year = year_dir.name
                files_by_year[year] = []
                
                # Check for annual file (year/file.csv)
                annual_pattern = f"DAT_MT_{pair.upper()}_M1_{year}.csv"
                annual_file = year_dir / annual_pattern
                
                if annual_file.exists():
                    files_by_year[year].append(annual_file)
                    logger.debug(f"Found annual file: {annual_file}")
                else:
                    # Check for monthly files (year/month/file.csv)
                    for month_dir in year_dir.iterdir():
                        if not month_dir.is_dir():
                            continue
                        
                        month = month_dir.name
                        monthly_pattern = f"DAT_MT_{pair.upper()}_M1_{year}{month.zfill(2)}.csv"
                        monthly_file = month_dir / monthly_pattern
                        
                        if monthly_file.exists():
                            files_by_year[year].append(monthly_file)
                            logger.debug(f"Found monthly file: {monthly_file}")
            
            # Cache the results
            self._file_cache[cache_key] = files_by_year
            
            total_files = sum(len(files) for files in files_by_year.values())
            logger.info(f"Discovered {total_files} files for {pair} across {len(files_by_year)} years")
            
        except Exception as e:
            logger.error(f"Error discovering files for {pair}: {str(e)}")
        
        return files_by_year
    
    @log_performance
    def load_csv_file(self, filepath: Path, validate: bool = True) -> Optional[pd.DataFrame]:
        """
        Load a single CSV file with histdata.com format.
        
        Args:
            filepath: Path to CSV file
            validate: Whether to validate the loaded data
            
        Returns:
            DataFrame with OHLCV data or None if error
        """
        try:
            if not filepath.exists():
                logger.error(f"File not found: {filepath}")
                return None
            
            # Log file info
            file_info = calculate_file_size(filepath)
            logger.debug(f"Loading file: {filepath} ({file_info.get('formatted', 'Unknown size')})")
            
            # Read CSV with appropriate settings
            df = pd.read_csv(
                filepath,
                header=None,
                names=['date', 'time', 'open', 'high', 'low', 'close', 'volume'],
                dtype={
                    'open': np.float64,
                    'high': np.float64,
                    'low': np.float64,
                    'close': np.float64,
                    'volume': np.int64
                },
                parse_dates=False  # We'll handle datetime parsing manually
            )
            
            if df.empty:
                logger.warning(f"Empty file: {filepath}")
                return None
            
            # Combine date and time columns
            df['datetime'] = pd.to_datetime(
                df['date'] + ' ' + df['time'],
                format=f"{self.date_format} {self.time_format}",
                errors='coerce'
            )
            
            # Drop original date and time columns
            df = df.drop(['date', 'time'], axis=1)
            
            # Remove rows with invalid datetime
            initial_rows = len(df)
            df = df.dropna(subset=['datetime'])
            
            if len(df) < initial_rows:
                logger.warning(f"Removed {initial_rows - len(df)} rows with invalid datetime")
            
            # Set datetime as index
            df = df.set_index('datetime')
            
            # Sort by datetime
            df = df.sort_index()
            
            # Validate data if requested
            if validate and not validate_dataframe(df.reset_index(), 
                                                 ['datetime', 'open', 'high', 'low', 'close', 'volume']):
                logger.error(f"Data validation failed for: {filepath}")
                return None
            
            logger.debug(f"Successfully loaded {len(df)} rows from {filepath}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading CSV file {filepath}: {str(e)}")
            return None
    
    @log_performance
    def load_data_range(
        self,
        pair: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_rows: Optional[int] = None
    ) -> Optional[pd.DataFrame]:
        """
        Load data for a specific date range.
        
        Args:
            pair: Currency pair
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            max_rows: Maximum number of rows to load
            
        Returns:
            Combined DataFrame or None if error
        """
        try:
            files_by_year = self.discover_files(pair)
            
            if not files_by_year:
                logger.warning(f"No files found for pair: {pair}")
                return None
            
            dataframes = []
            total_rows = 0
            
            # Sort years to load in reverse chronological order (newest first) for trading
            sorted_years = sorted(files_by_year.keys(), reverse=True)

            for year in sorted_years:
                if max_rows and total_rows >= max_rows:
                    break
                
                files = sorted(files_by_year[year], reverse=True)  # Newest files first

                for filepath in files:
                    if max_rows and total_rows >= max_rows:
                        break
                    
                    df = self.load_csv_file(filepath, validate=False)
                    
                    if df is None or df.empty:
                        continue
                    
                    # Filter by date range if specified
                    if start_date or end_date:
                        if start_date:
                            df = df[df.index >= start_date]
                        if end_date:
                            df = df[df.index <= end_date]
                    
                    if df.empty:
                        continue
                    
                    # Limit rows if specified (take most recent data)
                    if max_rows:
                        remaining_rows = max_rows - total_rows
                        if len(df) > remaining_rows:
                            df = df.tail(remaining_rows)
                    
                    dataframes.append(df)
                    total_rows += len(df)
                    
                    logger.debug(f"Loaded {len(df)} rows from {filepath.name}, total: {total_rows}")
            
            if not dataframes:
                logger.warning(f"No data found for {pair} in specified range")
                return None
            
            # Combine all dataframes
            combined_df = pd.concat(dataframes, axis=0)
            
            # Remove duplicates and sort
            combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
            combined_df = combined_df.sort_index()
            
            # Final validation
            if not validate_dataframe(combined_df.reset_index(), 
                                    ['datetime', 'open', 'high', 'low', 'close', 'volume']):
                logger.error(f"Final data validation failed for {pair}")
                return None
            
            logger.info(f"Successfully loaded {len(combined_df)} rows for {pair}")
            
            # Log memory usage
            mem_info = memory_usage()
            logger.debug(f"Memory usage after loading: {mem_info.get('rss_mb', 0):.1f} MB")
            
            return combined_df
            
        except Exception as e:
            logger.error(f"Error loading data range for {pair}: {str(e)}")
            return None
    
    def get_available_date_range(self, pair: str) -> Optional[Tuple[datetime, datetime]]:
        """
        Get the available date range for a currency pair.
        
        Args:
            pair: Currency pair
            
        Returns:
            Tuple of (start_date, end_date) or None if no data
        """
        try:
            files_by_year = self.discover_files(pair)
            
            if not files_by_year:
                return None
            
            # Get first and last file
            all_files = []
            for files in files_by_year.values():
                all_files.extend(files)
            
            if not all_files:
                return None
            
            all_files.sort()
            
            # Load first few rows from first file
            first_df = self.load_csv_file(all_files[0], validate=False)
            if first_df is None or first_df.empty:
                return None
            
            start_date = first_df.index.min()
            
            # Load last few rows from last file
            last_df = self.load_csv_file(all_files[-1], validate=False)
            if last_df is None or last_df.empty:
                end_date = first_df.index.max()
            else:
                end_date = last_df.index.max()
            
            logger.info(f"Available date range for {pair}: {start_date} to {end_date}")
            return start_date, end_date
            
        except Exception as e:
            logger.error(f"Error getting date range for {pair}: {str(e)}")
            return None
    
    def clear_cache(self):
        """Clear all cached data."""
        self._file_cache.clear()
        self._data_cache.clear()
        logger.info("Data loader cache cleared")
