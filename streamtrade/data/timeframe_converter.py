"""
Timeframe converter for converting M1 data to other timeframes.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from ..config.settings import settings
from ..config.logging_config import get_logger, log_performance
from ..core.utils import validate_dataframe

logger = get_logger(__name__)


class TimeframeConverter:
    """
    Converts M1 OHLCV data to other timeframes.
    
    Features:
    - Support for all standard timeframes
    - Efficient resampling algorithms
    - Data validation and integrity checks
    - Caching for performance
    """
    
    def __init__(self):
        self.supported_timeframes = settings.data_settings["supported_timeframes"]
        self.cache = {}
        
        # Timeframe to pandas frequency mapping
        self.freq_mapping = {
            "M1": "1min",    # 1 minute
            "M5": "5min",    # 5 minutes
            "M15": "15min",  # 15 minutes
            "M30": "30min",  # 30 minutes
            "H1": "1h",      # 1 hour
            "H4": "4h",      # 4 hours
            "D1": "1D",      # 1 day
            "W1": "1W",      # 1 week
            "MN1": "1M"      # 1 month (approximately)
        }
        
        logger.info(f"TimeframeConverter initialized with {len(self.supported_timeframes)} timeframes")
    
    def validate_timeframe(self, timeframe: str) -> bool:
        """
        Validate if timeframe is supported.
        
        Args:
            timeframe: Timeframe string (e.g., 'H1', 'D1')
            
        Returns:
            True if supported, False otherwise
        """
        return timeframe in self.supported_timeframes
    
    def get_multiplier(self, timeframe: str) -> int:
        """
        Get the multiplier for converting M1 to target timeframe.
        
        Args:
            timeframe: Target timeframe
            
        Returns:
            Multiplier value
        """
        return settings.get_timeframe_multiplier(timeframe)
    
    @log_performance
    def convert_timeframe(
        self,
        df: pd.DataFrame,
        target_timeframe: str,
        validate_input: bool = True,
        use_cache: bool = True
    ) -> Optional[pd.DataFrame]:
        """
        Convert OHLCV data to target timeframe.
        
        Args:
            df: Input DataFrame with datetime index and OHLCV columns
            target_timeframe: Target timeframe (M5, H1, D1, etc.)
            validate_input: Whether to validate input data
            use_cache: Whether to use caching
            
        Returns:
            Converted DataFrame or None if error
        """
        try:
            if not self.validate_timeframe(target_timeframe):
                logger.error(f"Unsupported timeframe: {target_timeframe}")
                return None
            
            # If already M1 and target is M1, return copy
            if target_timeframe == "M1":
                return df.copy()
            
            # Validate input data
            if validate_input:
                df_reset = df.reset_index()
                required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                if not validate_dataframe(df_reset, required_cols):
                    logger.error("Input data validation failed")
                    return None
            
            # Check cache
            cache_key = f"{id(df)}_{target_timeframe}"
            if use_cache and cache_key in self.cache:
                logger.debug(f"Using cached data for {target_timeframe}")
                return self.cache[cache_key].copy()
            
            # Ensure datetime index
            if not isinstance(df.index, pd.DatetimeIndex):
                logger.error("DataFrame must have datetime index")
                return None
            
            # Get pandas frequency
            freq = self.freq_mapping.get(target_timeframe)
            if not freq:
                logger.error(f"No frequency mapping for {target_timeframe}")
                return None
            
            # Perform resampling
            resampled = self._resample_ohlcv(df, freq, target_timeframe)
            
            if resampled is None or resampled.empty:
                logger.warning(f"Resampling resulted in empty DataFrame for {target_timeframe}")
                return None
            
            # Validate output
            if validate_input:
                resampled_reset = resampled.reset_index()
                if not validate_dataframe(resampled_reset, required_cols):
                    logger.error("Output data validation failed")
                    return None
            
            # Cache result
            if use_cache:
                self.cache[cache_key] = resampled.copy()
            
            logger.info(f"Successfully converted {len(df)} M1 candles to {len(resampled)} {target_timeframe} candles")
            return resampled
            
        except Exception as e:
            logger.error(f"Error converting to {target_timeframe}: {str(e)}")
            return None
    
    def _resample_ohlcv(self, df: pd.DataFrame, freq: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        Perform OHLCV resampling using pandas resample.
        
        Args:
            df: Input DataFrame
            freq: Pandas frequency string
            timeframe: Target timeframe for logging
            
        Returns:
            Resampled DataFrame
        """
        try:
            # Define aggregation rules for OHLCV data
            agg_rules = {
                'open': 'first',   # First value in period
                'high': 'max',     # Maximum value in period
                'low': 'min',      # Minimum value in period
                'close': 'last',   # Last value in period
                'volume': 'sum'    # Sum of volume
            }
            
            # Handle special cases for weekly and monthly data
            if timeframe == "W1":
                # Week starts on Monday (weekday=0)
                resampled = df.resample(freq, label='left', closed='left').agg(agg_rules)
            elif timeframe == "MN1":
                # Month starts on first day
                resampled = df.resample(freq, label='left', closed='left').agg(agg_rules)
            else:
                # Standard resampling
                resampled = df.resample(freq, label='left', closed='left').agg(agg_rules)
            
            # Remove rows where all OHLC values are NaN (no data in period)
            resampled = resampled.dropna(subset=['open', 'high', 'low', 'close'])
            
            # Ensure volume is integer
            if 'volume' in resampled.columns:
                resampled['volume'] = resampled['volume'].fillna(0).astype(np.int64)
            
            # Validate OHLC relationships
            resampled = self._validate_ohlc_relationships(resampled)
            
            return resampled
            
        except Exception as e:
            logger.error(f"Error in OHLCV resampling: {str(e)}")
            return None
    
    def _validate_ohlc_relationships(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and fix OHLC relationships.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with corrected OHLC relationships
        """
        try:
            # Check for invalid OHLC relationships
            invalid_high_low = df['high'] < df['low']
            invalid_open_high = df['open'] > df['high']
            invalid_open_low = df['open'] < df['low']
            invalid_close_high = df['close'] > df['high']
            invalid_close_low = df['close'] < df['low']
            
            total_invalid = (
                invalid_high_low.sum() + 
                invalid_open_high.sum() + 
                invalid_open_low.sum() + 
                invalid_close_high.sum() + 
                invalid_close_low.sum()
            )
            
            if total_invalid > 0:
                logger.warning(f"Found {total_invalid} OHLC relationship violations, attempting to fix")
                
                # Fix high < low by swapping
                swap_mask = invalid_high_low
                df.loc[swap_mask, ['high', 'low']] = df.loc[swap_mask, ['low', 'high']].values
                
                # Fix open/close outside high/low range
                df.loc[invalid_open_high, 'high'] = df.loc[invalid_open_high, 'open']
                df.loc[invalid_open_low, 'low'] = df.loc[invalid_open_low, 'open']
                df.loc[invalid_close_high, 'high'] = df.loc[invalid_close_high, 'close']
                df.loc[invalid_close_low, 'low'] = df.loc[invalid_close_low, 'close']
            
            return df
            
        except Exception as e:
            logger.error(f"Error validating OHLC relationships: {str(e)}")
            return df
    
    def convert_multiple_timeframes(
        self,
        df: pd.DataFrame,
        timeframes: list,
        validate_input: bool = True
    ) -> Dict[str, pd.DataFrame]:
        """
        Convert data to multiple timeframes at once.
        
        Args:
            df: Input M1 DataFrame
            timeframes: List of target timeframes
            validate_input: Whether to validate input data
            
        Returns:
            Dictionary with timeframe as key and DataFrame as value
        """
        results = {}
        
        try:
            for timeframe in timeframes:
                converted = self.convert_timeframe(
                    df, 
                    timeframe, 
                    validate_input=validate_input and timeframe == timeframes[0]  # Only validate once
                )
                
                if converted is not None:
                    results[timeframe] = converted
                    logger.debug(f"Successfully converted to {timeframe}: {len(converted)} candles")
                else:
                    logger.warning(f"Failed to convert to {timeframe}")
            
            logger.info(f"Successfully converted to {len(results)}/{len(timeframes)} timeframes")
            return results
            
        except Exception as e:
            logger.error(f"Error converting multiple timeframes: {str(e)}")
            return results
    
    def get_timeframe_info(self, timeframe: str) -> Dict[str, Any]:
        """
        Get information about a specific timeframe.
        
        Args:
            timeframe: Timeframe string
            
        Returns:
            Dictionary with timeframe information
        """
        if not self.validate_timeframe(timeframe):
            return {}
        
        return {
            'timeframe': timeframe,
            'multiplier': self.get_multiplier(timeframe),
            'pandas_freq': self.freq_mapping.get(timeframe),
            'description': self._get_timeframe_description(timeframe)
        }
    
    def _get_timeframe_description(self, timeframe: str) -> str:
        """Get human-readable description of timeframe."""
        descriptions = {
            "M1": "1 Minute",
            "M5": "5 Minutes",
            "M15": "15 Minutes",
            "M30": "30 Minutes",
            "H1": "1 Hour",
            "H4": "4 Hours",
            "D1": "1 Day",
            "W1": "1 Week",
            "MN1": "1 Month"
        }
        return descriptions.get(timeframe, timeframe)
    
    def clear_cache(self):
        """Clear conversion cache."""
        self.cache.clear()
        logger.info("Timeframe converter cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'cached_conversions': len(self.cache),
            'cache_keys': list(self.cache.keys())
        }
