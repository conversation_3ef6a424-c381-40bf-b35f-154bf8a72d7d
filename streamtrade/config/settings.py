"""
Settings and configuration management for StreamTrade platform.
"""

import os
from pathlib import Path
from typing import Dict, List, Any
import yaml


class Settings:
    """Central configuration management for StreamTrade."""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.project_dir = self.base_dir / "streamtrade"
        self.histdata_dir = self.base_dir / "histdata" / "MT" / "M1"
        
        # Data settings
        self.data_settings = {
            "chunk_size": 100000,  # Rows per chunk for large files
            "cache_size_mb": 500,  # Maximum cache size in MB
            "supported_timeframes": ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"],
            "default_timeframe": "H1",
            "timezone": "EST",
            "date_format": "%Y.%m.%d",
            "time_format": "%H:%M"
        }
        
        # Chart settings
        self.chart_settings = {
            "default_width": 1200,
            "default_height": 600,
            "max_candles_display": 10000,  # Maximum candles to display at once
            "price_precision": 4,  # Decimal places for price display
            "volume_precision": 0,
            "theme": "plotly_dark",
            "colors": {
                "bullish": "#26a69a",
                "bearish": "#ef5350",
                "volume": "#1f77b4",
                "background": "#1e1e1e"
            }
        }
        
        # Indicator settings
        self.indicator_settings = {
            "default_periods": {
                "sma": 20,
                "ema": 20,
                "rsi": 14,
                "macd": {"fast": 12, "slow": 26, "signal": 9},
                "bollinger": {"period": 20, "std": 2},
                "stochastic": {"k": 14, "d": 3}
            },
            "max_indicators": 10,  # Maximum indicators per chart
            "calculation_chunk_size": 50000
        }
        
        # Performance settings
        self.performance_settings = {
            "max_memory_mb": 2048,
            "enable_multiprocessing": True,
            "max_workers": 4,
            "cache_timeout_minutes": 30,
            "lazy_loading": True
        }
        
        # GUI settings
        self.gui_settings = {
            "page_title": "StreamTrade Platform",
            "page_icon": "📈",
            "layout": "wide",
            "sidebar_width": 300,
            "update_interval_ms": 1000
        }
        
        # Available currency pairs
        self.available_pairs = self._discover_available_pairs()
    
    def _discover_available_pairs(self) -> List[str]:
        """Discover available currency pairs from histdata directory."""
        pairs = []
        if self.histdata_dir.exists():
            for pair_dir in self.histdata_dir.iterdir():
                if pair_dir.is_dir():
                    pairs.append(pair_dir.name.upper())
        return sorted(pairs)
    
    def get_pair_data_path(self, pair: str) -> Path:
        """Get the data path for a specific currency pair."""
        return self.histdata_dir / pair.lower()
    
    def get_timeframe_multiplier(self, timeframe: str) -> int:
        """Get the multiplier for converting M1 to other timeframes."""
        multipliers = {
            "M1": 1,
            "M5": 5,
            "M15": 15,
            "M30": 30,
            "H1": 60,
            "H4": 240,
            "D1": 1440,
            "W1": 10080,
            "MN1": 43200  # Approximate
        }
        return multipliers.get(timeframe, 1)
    
    def validate_timeframe(self, timeframe: str) -> bool:
        """Validate if timeframe is supported."""
        return timeframe in self.data_settings["supported_timeframes"]
    
    def validate_pair(self, pair: str) -> bool:
        """Validate if currency pair is available."""
        return pair.upper() in self.available_pairs
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get all configuration as dictionary."""
        return {
            "data": self.data_settings,
            "chart": self.chart_settings,
            "indicator": self.indicator_settings,
            "performance": self.performance_settings,
            "gui": self.gui_settings,
            "pairs": self.available_pairs
        }
    
    def save_config(self, filepath: str = None):
        """Save current configuration to YAML file."""
        if filepath is None:
            filepath = self.project_dir / "config" / "settings.yaml"
        
        config = self.get_config_dict()
        with open(filepath, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
    
    def load_config(self, filepath: str = None):
        """Load configuration from YAML file."""
        if filepath is None:
            filepath = self.project_dir / "config" / "settings.yaml"
        
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                config = yaml.safe_load(f)
                
            # Update settings with loaded config
            if "data" in config:
                self.data_settings.update(config["data"])
            if "chart" in config:
                self.chart_settings.update(config["chart"])
            if "indicator" in config:
                self.indicator_settings.update(config["indicator"])
            if "performance" in config:
                self.performance_settings.update(config["performance"])
            if "gui" in config:
                self.gui_settings.update(config["gui"])


# Global settings instance
settings = Settings()
