"""
Technical indicators implementation using pandas-ta.
"""

import pandas as pd
import numpy as np
from typing import Dict, List

# Use custom implementations for better compatibility
PANDAS_TA_AVAILABLE = False

from .base_indicator import BaseIndicator, IndicatorParameter
from ..config.logging_config import get_logger

logger = get_logger(__name__)

# Fallback implementations if pandas-ta is not available
def simple_sma(series: pd.Series, period: int) -> pd.Series:
    """Simple Moving Average implementation."""
    return series.rolling(window=period).mean()

def simple_ema(series: pd.Series, period: int) -> pd.Series:
    """Exponential Moving Average implementation."""
    return series.ewm(span=period).mean()

def simple_rsi(series: pd.Series, period: int = 14) -> pd.Series:
    """Relative Strength Index implementation."""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def simple_macd(series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
    """MACD implementation."""
    ema_fast = simple_ema(series, fast)
    ema_slow = simple_ema(series, slow)
    macd_line = ema_fast - ema_slow
    signal_line = simple_ema(macd_line, signal)
    histogram = macd_line - signal_line

    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }

def simple_bollinger_bands(series: pd.Series, period: int = 20, std: float = 2.0) -> Dict[str, pd.Series]:
    """Bollinger Bands implementation."""
    sma = simple_sma(series, period)
    rolling_std = series.rolling(window=period).std()

    upper = sma + (rolling_std * std)
    lower = sma - (rolling_std * std)

    return {
        'upper': upper,
        'middle': sma,
        'lower': lower
    }

def simple_stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
    """Stochastic Oscillator implementation."""
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()

    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()

    return {
        'k': k_percent,
        'd': d_percent
    }

def simple_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """Average True Range implementation."""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())

    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = true_range.rolling(window=period).mean()

    return atr


class SMA(BaseIndicator):
    """Simple Moving Average indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=1,
                max_value=200,
                description="Number of periods for moving average"
            )
        ]
    
    def _get_description(self) -> str:
        return "Simple Moving Average - arithmetic mean of prices over specified periods"
    
    def _get_category(self) -> str:
        return "trend"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                sma = ta.sma(data['close'], length=period)
            except Exception:
                sma = simple_sma(data['close'], period)
        else:
            sma = simple_sma(data['close'], period)

        return {'sma': sma}


class EMA(BaseIndicator):
    """Exponential Moving Average indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=1,
                max_value=200,
                description="Number of periods for exponential moving average"
            )
        ]
    
    def _get_description(self) -> str:
        return "Exponential Moving Average - weighted average giving more importance to recent prices"
    
    def _get_category(self) -> str:
        return "trend"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                ema = ta.ema(data['close'], length=period)
            except Exception:
                ema = simple_ema(data['close'], period)
        else:
            ema = simple_ema(data['close'], period)

        return {'ema': ema}


class RSI(BaseIndicator):
    """Relative Strength Index indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=14,
                min_value=2,
                max_value=100,
                description="Number of periods for RSI calculation"
            )
        ]
    
    def _get_description(self) -> str:
        return "Relative Strength Index - momentum oscillator measuring speed and change of price movements"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                rsi = ta.rsi(data['close'], length=period)
            except Exception:
                rsi = simple_rsi(data['close'], period)
        else:
            rsi = simple_rsi(data['close'], period)

        return {'rsi': rsi}


class MACD(BaseIndicator):
    """MACD (Moving Average Convergence Divergence) indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="fast",
                type=int,
                default=12,
                min_value=1,
                max_value=50,
                description="Fast EMA period"
            ),
            IndicatorParameter(
                name="slow",
                type=int,
                default=26,
                min_value=1,
                max_value=100,
                description="Slow EMA period"
            ),
            IndicatorParameter(
                name="signal",
                type=int,
                default=9,
                min_value=1,
                max_value=50,
                description="Signal line EMA period"
            )
        ]
    
    def _get_description(self) -> str:
        return "MACD - trend-following momentum indicator showing relationship between two moving averages"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        fast = kwargs['fast']
        slow = kwargs['slow']
        signal = kwargs['signal']

        macd_data = simple_macd(data['close'], fast=fast, slow=slow, signal=signal)

        return macd_data


class BollingerBands(BaseIndicator):
    """Bollinger Bands indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=2,
                max_value=100,
                description="Number of periods for moving average"
            ),
            IndicatorParameter(
                name="std",
                type=float,
                default=2.0,
                min_value=0.1,
                max_value=5.0,
                description="Number of standard deviations"
            )
        ]
    
    def _get_description(self) -> str:
        return "Bollinger Bands - volatility indicator with upper and lower bands around moving average"
    
    def _get_category(self) -> str:
        return "volatility"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']
        std = kwargs['std']

        bb_data = simple_bollinger_bands(data['close'], period=period, std=std)

        return bb_data


class Stochastic(BaseIndicator):
    """Stochastic Oscillator indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="k_period",
                type=int,
                default=14,
                min_value=1,
                max_value=100,
                description="K period for stochastic calculation"
            ),
            IndicatorParameter(
                name="d_period",
                type=int,
                default=3,
                min_value=1,
                max_value=50,
                description="D period for signal line"
            )
        ]
    
    def _get_description(self) -> str:
        return "Stochastic Oscillator - momentum indicator comparing closing price to price range"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        k_period = kwargs['k_period']
        d_period = kwargs['d_period']

        stoch_data = simple_stochastic(
            data['high'], data['low'], data['close'],
            k_period=k_period, d_period=d_period
        )

        return stoch_data


class ATR(BaseIndicator):
    """Average True Range indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=14,
                min_value=1,
                max_value=100,
                description="Number of periods for ATR calculation"
            )
        ]
    
    def _get_description(self) -> str:
        return "Average True Range - volatility indicator measuring market volatility"
    
    def _get_category(self) -> str:
        return "volatility"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']
        atr = simple_atr(data['high'], data['low'], data['close'], period)
        return {'atr': atr}


class Volume(BaseIndicator):
    """Volume indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="show_ma",
                type=bool,
                default=True,
                description="Show volume moving average"
            ),
            IndicatorParameter(
                name="ma_period",
                type=int,
                default=20,
                min_value=1,
                max_value=100,
                description="Moving average period for volume"
            )
        ]
    
    def _get_description(self) -> str:
        return "Volume - trading volume with optional moving average"
    
    def _get_category(self) -> str:
        return "volume"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        show_ma = kwargs['show_ma']
        ma_period = kwargs['ma_period']
        
        result = {'volume': data['volume']}
        
        if show_ma:
            volume_ma = simple_sma(data['volume'], ma_period)
            result['volume_ma'] = volume_ma
        
        return result


class TechnicalIndicators:
    """
    Factory class for technical indicators.
    """
    
    # Available indicators
    INDICATORS = {
        'SMA': SMA,
        'EMA': EMA,
        'RSI': RSI,
        'MACD': MACD,
        'BollingerBands': BollingerBands,
        'Stochastic': Stochastic,
        'ATR': ATR,
        'Volume': Volume
    }
    
    @classmethod
    def get_available_indicators(cls) -> Dict[str, str]:
        """Get list of available indicators with descriptions."""
        indicators = {}
        for name, indicator_class in cls.INDICATORS.items():
            instance = indicator_class()
            indicators[name] = instance.description
        return indicators
    
    @classmethod
    def get_indicator(cls, name: str) -> BaseIndicator:
        """
        Get indicator instance by name.
        
        Args:
            name: Indicator name
            
        Returns:
            Indicator instance
            
        Raises:
            ValueError: If indicator not found
        """
        if name not in cls.INDICATORS:
            available = list(cls.INDICATORS.keys())
            raise ValueError(f"Indicator '{name}' not found. Available: {available}")
        
        return cls.INDICATORS[name]()
    
    @classmethod
    def get_indicators_by_category(cls) -> Dict[str, List[str]]:
        """Get indicators grouped by category."""
        categories = {}
        
        for name, indicator_class in cls.INDICATORS.items():
            instance = indicator_class()
            category = instance.category
            
            if category not in categories:
                categories[category] = []
            
            categories[category].append(name)
        
        return categories
