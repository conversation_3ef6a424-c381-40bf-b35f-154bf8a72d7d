"""
StreamTrade Platform - Advanced Trading and Backtesting System

A comprehensive platform for forex trading analysis, backtesting, and visualization.
"""

__version__ = "0.1.0"
__author__ = "StreamTrade Development Team"
__description__ = "Advanced Trading and Backtesting Platform"

# Core imports
from .config import Settings, setup_logging
from .data import DataManager, DataLoader, TimeframeConverter
from .indicators import TechnicalIndicators, IndicatorManager
from .visualization import ChartViewer, PlotlyCharts
from .gui import StreamTradeApp

# Initialize logging
setup_logging(level="INFO", console_output=True)

__all__ = [
    'Settings',
    'setup_logging',
    'DataManager',
    'DataLoader',
    'TimeframeConverter',
    'TechnicalIndicators',
    'IndicatorManager',
    'ChartViewer',
    'PlotlyCharts',
    'StreamTradeApp'
]
