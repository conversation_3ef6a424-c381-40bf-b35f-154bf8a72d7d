"""
Plotly chart implementation for StreamTrade platform.
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from ..config.settings import settings
from ..config.logging_config import get_logger
from ..indicators.base_indicator import IndicatorResult

logger = get_logger(__name__)


class PlotlyCharts:
    """
    Plotly-based chart implementation for financial data visualization.
    
    Features:
    - Candlestick charts with OHLCV data
    - Multiple indicator overlays
    - Responsive design
    - Interactive features (zoom, pan, crosshair)
    - Multiple subplots for different indicator types
    """
    
    def __init__(self):
        self.chart_settings = settings.chart_settings
        self.colors = self.chart_settings["colors"]
        self.price_precision = self.chart_settings["price_precision"]
        
        logger.debug("PlotlyCharts initialized")
    
    def create_candlestick_chart(
        self,
        data: pd.DataFrame,
        indicators: Optional[Dict[str, IndicatorResult]] = None,
        title: str = "Price Chart",
        height: Optional[int] = None,
        width: Optional[int] = None,
        remove_gaps: bool = True,
        chart_style: str = "Candlestick",
        show_vertical_line: bool = True,
        show_horizontal_line: bool = True
    ) -> go.Figure:
        """
        Create a candlestick chart with optional indicators.

        Args:
            data: OHLCV DataFrame with datetime index
            indicators: Dictionary of indicator results
            title: Chart title
            height: Chart height in pixels
            width: Chart width in pixels
            remove_gaps: Whether to remove weekend gaps

        Returns:
            Plotly Figure object
        """
        try:
            # Set default dimensions
            if height is None:
                height = self.chart_settings["default_height"]
            if width is None:
                width = self.chart_settings["default_width"]

            # Process data for gap removal if requested
            processed_data = self._process_data_for_gaps(data, remove_gaps) if remove_gaps else data

            # Determine subplot configuration
            subplot_config = self._determine_subplot_layout(indicators)

            # Create subplots
            fig = make_subplots(
                rows=subplot_config['rows'],
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                subplot_titles=subplot_config['titles'],
                row_heights=subplot_config['heights']
            )

            # Add candlestick chart
            self._add_candlestick(fig, processed_data, row=1, remove_gaps=remove_gaps, chart_style=chart_style)

            # Add indicators
            if indicators:
                self._add_indicators(fig, processed_data, indicators, subplot_config, remove_gaps=remove_gaps)

            # Update layout with enhanced features
            self._update_layout(fig, title, height, width, len(processed_data), remove_gaps=remove_gaps,
                              show_vertical_line=show_vertical_line, show_horizontal_line=show_horizontal_line)

            logger.debug(f"Created candlestick chart with {len(processed_data)} candles")
            return fig
            
        except Exception as e:
            logger.error(f"Error creating candlestick chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def _determine_subplot_layout(
        self,
        indicators: Optional[Dict[str, IndicatorResult]]
    ) -> Dict[str, Any]:
        """
        Determine subplot layout based on indicators.
        
        Args:
            indicators: Dictionary of indicator results
            
        Returns:
            Subplot configuration
        """
        config = {
            'rows': 1,
            'titles': ['Price'],
            'heights': [1.0],
            'indicator_rows': {}
        }
        
        if not indicators:
            return config
        
        # Categorize indicators by display type
        overlay_indicators = []  # Display on price chart
        separate_indicators = []  # Display in separate subplots
        
        for name, result in indicators.items():
            category = result.metadata.get('category', 'unknown')
            
            if category in ['trend', 'volatility']:
                overlay_indicators.append((name, result))
            else:
                separate_indicators.append((name, result))
        
        # Add separate subplots for non-overlay indicators
        current_row = 1
        
        for name, result in separate_indicators:
            current_row += 1
            config['rows'] = current_row
            config['titles'].append(name)
            config['indicator_rows'][name] = current_row
        
        # Adjust heights (price chart gets more space)
        if config['rows'] > 1:
            price_height = 0.6
            indicator_height = 0.4 / (config['rows'] - 1)
            config['heights'] = [price_height] + [indicator_height] * (config['rows'] - 1)
        
        return config

    def _process_data_for_gaps(self, data: pd.DataFrame, remove_gaps: bool) -> pd.DataFrame:
        """
        Process data to remove weekend gaps if requested.

        Args:
            data: Input DataFrame
            remove_gaps: Whether to remove gaps

        Returns:
            Processed DataFrame
        """
        if not remove_gaps:
            return data

        try:
            # Create a copy to avoid modifying original data
            processed_data = data.copy()

            # Reset index to work with datetime
            processed_data = processed_data.reset_index()

            # Store original datetime for hover info
            processed_data['original_datetime'] = processed_data['datetime']

            # Create sequential datetime index that preserves visual spacing
            # Use the first datetime as origin and create regular intervals
            start_time = processed_data['datetime'].iloc[0]

            # Determine appropriate time interval based on data frequency
            if len(processed_data) > 1:
                # Calculate average time difference between consecutive candles
                time_diffs = processed_data['datetime'].diff().dropna()
                avg_interval = time_diffs.median()

                # Use the median interval for consistent spacing
                # This preserves the visual rhythm of the data
                new_datetimes = [start_time + i * avg_interval for i in range(len(processed_data))]
            else:
                # Single data point case
                new_datetimes = [start_time]

            # Replace datetime with sequential datetime
            processed_data['datetime'] = new_datetimes

            # Set datetime as index again
            processed_data = processed_data.set_index('datetime')

            logger.debug(f"Processed {len(processed_data)} candles for gap removal")
            return processed_data

        except Exception as e:
            logger.error(f"Error processing data for gaps: {str(e)}")
            return data

    def _add_candlestick(self, fig: go.Figure, data: pd.DataFrame, row: int = 1, remove_gaps: bool = True, chart_style: str = "Candlestick"):
        """Add candlestick/OHLC/line trace to figure based on chart_style."""
        try:
            # Prepare hover text
            if remove_gaps and 'original_datetime' in data.columns:
                # Use original datetime for hover when gaps are removed
                hover_text = [
                    f"Time: {orig_dt}<br>" +
                    f"Open: {o:.4f}<br>" +
                    f"High: {h:.4f}<br>" +
                    f"Low: {l:.4f}<br>" +
                    f"Close: {c:.4f}<br>" +
                    f"Volume: {v:,.0f}"
                    for orig_dt, o, h, l, c, v in zip(
                        data['original_datetime'],
                        data['open'], data['high'], data['low'], data['close'], data['volume']
                    )
                ]
                use_custom_hover = True
            else:
                hover_text = None
                use_custom_hover = False

            # Create trace based on chart style
            # Ensure consistent timestamp format for all traces
            x_timestamps = [pd.Timestamp(ts) for ts in data.index]

            if chart_style == "Candlestick":
                trace = go.Candlestick(
                    x=x_timestamps,
                    open=data['open'],
                    high=data['high'],
                    low=data['low'],
                    close=data['close'],
                    name='Price',
                    increasing_line_color=self.colors['bullish'],
                    decreasing_line_color=self.colors['bearish'],
                    increasing_fillcolor=self.colors['bullish'],
                    decreasing_fillcolor=self.colors['bearish'],
                    hovertext=hover_text if use_custom_hover else None,
                    hoverinfo='text' if use_custom_hover else None
                )
            elif chart_style == "OHLC Bars":
                trace = go.Ohlc(
                    x=x_timestamps,
                    open=data['open'],
                    high=data['high'],
                    low=data['low'],
                    close=data['close'],
                    name='Price',
                    increasing_line_color=self.colors['bullish'],
                    decreasing_line_color=self.colors['bearish'],
                    hovertext=hover_text if use_custom_hover else None,
                    hoverinfo='text' if use_custom_hover else None
                )
            elif chart_style == "Line":
                trace = go.Scatter(
                    x=x_timestamps,
                    y=data['close'],
                    mode='lines',
                    name='Close Price',
                    line=dict(color=self.colors['bullish'], width=2),
                    hovertemplate='<b>%{x}</b><br>Close: %{y:.4f}<extra></extra>' if not use_custom_hover else None,
                    hovertext=hover_text if use_custom_hover else None,
                    hoverinfo='text' if use_custom_hover else None
                )
            else:
                # Default to candlestick
                trace = go.Candlestick(
                    x=x_timestamps,
                    open=data['open'],
                    high=data['high'],
                    low=data['low'],
                    close=data['close'],
                    name='Price',
                    increasing_line_color=self.colors['bullish'],
                    decreasing_line_color=self.colors['bearish'],
                    increasing_fillcolor=self.colors['bullish'],
                    decreasing_fillcolor=self.colors['bearish'],
                    hovertext=hover_text if use_custom_hover else None,
                    hoverinfo='text' if use_custom_hover else None
                )

            fig.add_trace(trace, row=row, col=1)
            
            # Volume will be handled separately in subplot configuration
            
        except Exception as e:
            logger.error(f"Error adding candlestick: {str(e)}")
    
    def _add_volume_subplot(self, fig: go.Figure, data: pd.DataFrame, row: int):
        """Add volume subplot if volume data is available."""
        try:
            # Create volume colors based on price movement
            colors = []
            for i in range(len(data)):
                if i == 0:
                    colors.append(self.colors['volume'])
                else:
                    if data['close'].iloc[i] >= data['close'].iloc[i-1]:
                        colors.append(self.colors['bullish'])
                    else:
                        colors.append(self.colors['bearish'])
            
            volume_trace = go.Bar(
                x=data.index,
                y=data['volume'],
                name='Volume',
                marker_color=colors,
                opacity=0.7
            )
            
            fig.add_trace(volume_trace, row=row, col=1)
            
        except Exception as e:
            logger.error(f"Error adding volume: {str(e)}")
    
    def _add_indicators(
        self,
        fig: go.Figure,
        data: pd.DataFrame,
        indicators: Dict[str, IndicatorResult],
        subplot_config: Dict[str, Any],
        remove_gaps: bool = True
    ):
        """Add indicator traces to figure."""
        try:
            for name, result in indicators.items():
                category = result.metadata.get('category', 'unknown')

                if category in ['trend', 'volatility']:
                    # Add to price chart (row 1)
                    self._add_overlay_indicator(fig, result, data, row=1)
                else:
                    # Add to separate subplot
                    row = subplot_config['indicator_rows'].get(name, 2)
                    self._add_separate_indicator(fig, result, data, row=row)

        except Exception as e:
            logger.error(f"Error adding indicators: {str(e)}")
    
    def _add_overlay_indicator(self, fig: go.Figure, result: IndicatorResult, processed_data: pd.DataFrame, row: int = 1):
        """Add overlay indicator to price chart."""
        try:
            for series_name, series_data in result.data.items():
                if series_data is None or series_data.empty:
                    continue

                # Synchronize indicator timeline with processed data timeline
                synced_x, synced_y = self._sync_indicator_timeline(series_data, processed_data)

                # Determine line style and color
                line_style = self._get_indicator_style(result.name, series_name, result)

                trace = go.Scatter(
                    x=synced_x,
                    y=synced_y,
                    mode='lines',
                    name=f"{result.name} {series_name}",
                    line=line_style,
                    opacity=0.8
                )

                fig.add_trace(trace, row=row, col=1)

            # Special handling for Ichimoku cloud
            if result.name == 'Ichimoku':
                self._add_ichimoku_cloud(fig, result, processed_data, row)

        except Exception as e:
            logger.error(f"Error adding overlay indicator {result.name}: {str(e)}")
    
    def _add_separate_indicator(self, fig: go.Figure, result: IndicatorResult, processed_data: pd.DataFrame, row: int):
        """Add indicator to separate subplot."""
        try:
            for series_name, series_data in result.data.items():
                if series_data is None or series_data.empty:
                    continue

                # Synchronize indicator timeline with processed data timeline
                synced_x, synced_y = self._sync_indicator_timeline(series_data, processed_data)

                line_style = self._get_indicator_style(result.name, series_name, result)

                trace = go.Scatter(
                    x=synced_x,
                    y=synced_y,
                    mode='lines',
                    name=f"{result.name} {series_name}",
                    line=line_style
                )

                fig.add_trace(trace, row=row, col=1)

                # Add reference lines for oscillators
                if result.metadata.get('category') == 'momentum':
                    self._add_oscillator_reference_lines(fig, result.name, row)

        except Exception as e:
            logger.error(f"Error adding separate indicator {result.name}: {str(e)}")

    def _sync_indicator_timeline(self, indicator_series: pd.Series, processed_data: pd.DataFrame) -> tuple:
        """
        Synchronize indicator timeline with processed data timeline.

        Args:
            indicator_series: Original indicator series with original datetime index
            processed_data: Processed data with gap-removed timeline

        Returns:
            Tuple of (synced_x, synced_y) for plotting
        """
        try:
            # If processed_data has original_datetime column, we can map properly
            if 'original_datetime' in processed_data.columns:
                # Create mapping from original datetime to processed datetime
                datetime_mapping = dict(zip(processed_data['original_datetime'], processed_data.index))

                # Map indicator timestamps to processed timestamps
                synced_x = []
                synced_y = []

                for timestamp, value in indicator_series.items():
                    if pd.notna(value) and timestamp in datetime_mapping:
                        # Ensure consistent timestamp format
                        mapped_timestamp = datetime_mapping[timestamp]
                        synced_x.append(pd.Timestamp(mapped_timestamp))
                        synced_y.append(value)

                logger.debug(f"Synced {len(synced_x)} indicator points out of {len(indicator_series.dropna())} valid points")
                return synced_x, synced_y
            else:
                # No gap removal was applied, use processed_data timeline for consistency
                # Map indicator timestamps to processed_data timestamps to ensure format consistency
                valid_data = indicator_series.dropna()
                synced_x = []
                synced_y = []

                for timestamp, value in valid_data.items():
                    # Find matching timestamp in processed_data with exact match
                    matching_mask = processed_data.index == timestamp
                    if matching_mask.any():
                        # Use the processed_data timestamp format for consistency
                        matching_timestamp = processed_data.index[matching_mask][0]
                        synced_x.append(pd.Timestamp(matching_timestamp))
                        synced_y.append(value)
                    else:
                        # If no exact match, try to find closest timestamp
                        closest_idx = processed_data.index.get_indexer([timestamp], method='nearest')[0]
                        if closest_idx >= 0 and closest_idx < len(processed_data.index):
                            synced_x.append(pd.Timestamp(processed_data.index[closest_idx]))
                            synced_y.append(value)

                logger.debug(f"Mapped {len(synced_x)} indicator points to processed timeline format")
                return synced_x, synced_y

        except Exception as e:
            logger.error(f"Error syncing indicator timeline: {str(e)}")
            # Fallback: use processed_data timeline format
            valid_data = indicator_series.dropna()
            synced_x = []
            synced_y = []

            for timestamp, value in valid_data.items():
                # Find matching timestamp in processed_data
                matching_mask = processed_data.index == timestamp
                if matching_mask.any():
                    synced_x.append(pd.Timestamp(processed_data.index[matching_mask][0]))
                    synced_y.append(value)
                else:
                    # If no exact match, try to find closest timestamp
                    try:
                        closest_idx = processed_data.index.get_indexer([timestamp], method='nearest')[0]
                        if closest_idx >= 0 and closest_idx < len(processed_data.index):
                            synced_x.append(pd.Timestamp(processed_data.index[closest_idx]))
                            synced_y.append(value)
                    except:
                        # Last resort: use original timestamp with consistent format
                        synced_x.append(pd.Timestamp(timestamp))
                        synced_y.append(value)

            return synced_x, synced_y

    def _add_ichimoku_cloud(self, fig: go.Figure, result, processed_data: pd.DataFrame, row: int):
        """Add Ichimoku cloud (Kumo) between Senkou Span A and B."""
        try:
            senkou_a_data = result.data.get('senkou_span_a')
            senkou_b_data = result.data.get('senkou_span_b')

            if senkou_a_data is None or senkou_b_data is None:
                return

            # Synchronize both series
            synced_a_x, synced_a_y = self._sync_indicator_timeline(senkou_a_data, processed_data)
            synced_b_x, synced_b_y = self._sync_indicator_timeline(senkou_b_data, processed_data)

            if not synced_a_x or not synced_b_x:
                return

            # Ensure both series have the same x values
            common_x = []
            a_values = []
            b_values = []

            # Create dictionaries for easier lookup
            a_dict = dict(zip(synced_a_x, synced_a_y))
            b_dict = dict(zip(synced_b_x, synced_b_y))

            # Find common timestamps
            for x in synced_a_x:
                if x in b_dict:
                    common_x.append(x)
                    a_values.append(a_dict[x])
                    b_values.append(b_dict[x])

            if len(common_x) < 2:
                return

            # Get cloud colors from parameters
            params = result.parameters if hasattr(result, 'parameters') else {}
            bullish_cloud_color = params.get('senkou_span_a_color', '#00ff00')
            bearish_cloud_color = params.get('senkou_span_b_color', '#ff8000')

            # Create cloud fill
            # When Senkou A > Senkou B: bullish cloud (green)
            # When Senkou A < Senkou B: bearish cloud (red)

            # Add fill between the lines
            fig.add_trace(
                go.Scatter(
                    x=common_x + common_x[::-1],  # x values forward and backward
                    y=a_values + b_values[::-1],  # y values forward and backward
                    fill='toself',
                    fillcolor='rgba(0, 255, 0, 0.1)',  # Light green with transparency
                    line=dict(color='rgba(255,255,255,0)'),  # Invisible line
                    hoverinfo="skip",
                    showlegend=False,
                    name="Ichimoku Cloud"
                ),
                row=row, col=1
            )

        except Exception as e:
            logger.error(f"Error adding Ichimoku cloud: {str(e)}")

    def _add_oscillator_reference_lines(self, fig: go.Figure, indicator_name: str, row: int):
        """Add reference lines for oscillator indicators."""
        try:
            if indicator_name == 'RSI':
                # Add 30 and 70 lines for RSI
                for level, color in [(30, 'green'), (70, 'red')]:
                    fig.add_hline(
                        y=level,
                        line_dash="dash",
                        line_color=color,
                        opacity=0.5,
                        row=row,
                        col=1
                    )
            elif indicator_name == 'Stochastic':
                # Add 20 and 80 lines for Stochastic
                for level, color in [(20, 'green'), (80, 'red')]:
                    fig.add_hline(
                        y=level,
                        line_dash="dash",
                        line_color=color,
                        opacity=0.5,
                        row=row,
                        col=1
                    )
                    
        except Exception as e:
            logger.error(f"Error adding reference lines: {str(e)}")
    
    def _get_indicator_style(self, indicator_name: str, series_name: str, indicator_result=None) -> Dict[str, Any]:
        """Get line style for indicator series."""
        # Default style
        style = {'width': 2}

        # Try to get color from indicator parameters first
        if indicator_result and hasattr(indicator_result, 'parameters'):
            params = indicator_result.parameters

            # For single-line indicators (SMA, EMA)
            if indicator_name in ['SMA', 'EMA'] and 'color' in params:
                style['color'] = params['color']
                return style

            # For multi-line indicators
            elif indicator_name == 'BollingerBands':
                if 'upper' in series_name and 'upper_color' in params:
                    style['color'] = params['upper_color']
                    style['dash'] = 'dash'
                elif 'lower' in series_name and 'lower_color' in params:
                    style['color'] = params['lower_color']
                    style['dash'] = 'dash'
                elif 'middle' in series_name and 'middle_color' in params:
                    style['color'] = params['middle_color']
                return style

            elif indicator_name == 'Ichimoku':
                if 'tenkan_sen' in series_name and 'tenkan_color' in params:
                    style['color'] = params['tenkan_color']
                elif 'kijun_sen' in series_name and 'kijun_color' in params:
                    style['color'] = params['kijun_color']
                elif 'senkou_span_a' in series_name and 'senkou_span_a_color' in params:
                    style['color'] = params['senkou_span_a_color']
                elif 'senkou_span_b' in series_name and 'senkou_span_b_color' in params:
                    style['color'] = params['senkou_span_b_color']
                elif 'chikou_span' in series_name and 'chikou_color' in params:
                    style['color'] = params['chikou_color']
                return style

        # Fallback to default colors if no custom colors specified
        if indicator_name == 'SMA':
            style['color'] = '#1f77b4'  # Blue
        elif indicator_name == 'EMA':
            style['color'] = '#ff7f0e'  # Orange
        elif indicator_name == 'BollingerBands':
            if 'upper' in series_name or 'lower' in series_name:
                style['color'] = '#ff0000'  # Red
                style['dash'] = 'dash'
            else:
                style['color'] = '#0000ff'  # Blue
        elif indicator_name == 'MACD':
            if 'signal' in series_name:
                style['color'] = '#ff0000'  # Red
            elif 'histogram' in series_name:
                style['color'] = '#808080'  # Gray
            else:
                style['color'] = '#0000ff'  # Blue
        elif indicator_name == 'RSI':
            style['color'] = '#800080'  # Purple
        elif indicator_name == 'Stochastic':
            if 'k' in series_name:
                style['color'] = '#0000ff'  # Blue
            else:
                style['color'] = '#ff0000'  # Red
        elif indicator_name == 'Ichimoku':
            if 'tenkan_sen' in series_name:
                style['color'] = '#ff0000'  # Red
            elif 'kijun_sen' in series_name:
                style['color'] = '#0000ff'  # Blue
            elif 'senkou_span_a' in series_name:
                style['color'] = '#00ff00'  # Green
            elif 'senkou_span_b' in series_name:
                style['color'] = '#ff8000'  # Orange
            elif 'chikou_span' in series_name:
                style['color'] = '#800080'  # Purple

        return style
    
    def _update_layout(
        self,
        fig: go.Figure,
        title: str,
        height: int,
        width: int,
        data_points: int,
        remove_gaps: bool = True,
        show_vertical_line: bool = True,
        show_horizontal_line: bool = True
    ):
        """Update figure layout with styling and configuration."""
        try:
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'xanchor': 'center'
                },
                height=height,
                width=width,
                template=self.chart_settings["theme"],
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                margin=dict(l=50, r=50, t=80, b=50),
                hovermode='x unified',
                # Enable interactive features
                dragmode='pan',  # Default to pan mode
                selectdirection='h'  # Horizontal selection
            )

            # Update x-axis with enhanced interactivity and crosshair
            fig.update_xaxes(
                title_text="Time",
                rangeslider_visible=False,
                type='date',
                # Enable zooming and panning
                fixedrange=False,
                # Show grid for better readability
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                # Enable vertical crosshair spikes
                showspikes=show_vertical_line,
                spikecolor="rgba(255,255,255,0.8)" if show_vertical_line else None,
                spikesnap="cursor" if show_vertical_line else None,
                spikemode="across" if show_vertical_line else None,
                spikethickness=1 if show_vertical_line else 0,
                spikedash="dot" if show_vertical_line else None
            )

            # Update y-axis for price chart with TradingView-like scaling and crosshair
            fig.update_yaxes(
                title_text="Price",
                tickformat=f'.{self.price_precision}f',
                row=1,
                col=1,
                # Enable price scaling like TradingView
                fixedrange=False,
                scaleanchor=None,  # Allow independent scaling
                scaleratio=None,   # Allow free scaling
                # Show grid for better readability
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                # Enable side drag for price scaling
                side='right',
                # Custom tick configuration
                automargin=True,
                # Enable double-click reset
                autorange=True,
                # Enable horizontal crosshair spikes with price level
                showspikes=show_horizontal_line,
                spikecolor="rgba(255,255,255,0.8)" if show_horizontal_line else None,
                spikesnap="cursor" if show_horizontal_line else None,
                spikemode="across" if show_horizontal_line else None,
                spikethickness=1 if show_horizontal_line else 0,
                # Show price level on spike with dotted line
                spikedash="dot" if show_horizontal_line else None
            )

            # Limit data points for performance
            max_candles = self.chart_settings["max_candles_display"]
            if data_points > max_candles:
                logger.warning(f"Data points ({data_points}) exceed max display limit ({max_candles})")

        except Exception as e:
            logger.error(f"Error updating layout: {str(e)}")
    
    def _create_error_chart(self, error_message: str) -> go.Figure:
        """Create an error chart when chart creation fails."""
        fig = go.Figure()
        
        fig.add_annotation(
            text=f"Error creating chart:<br>{error_message}",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16, color="red")
        )
        
        fig.update_layout(
            title="Chart Error",
            height=400,
            width=800
        )
        
        return fig
    
    def create_simple_line_chart(
        self,
        data: pd.Series,
        title: str = "Line Chart",
        color: str = "blue"
    ) -> go.Figure:
        """
        Create a simple line chart for single series data.
        
        Args:
            data: Pandas Series with datetime index
            title: Chart title
            color: Line color
            
        Returns:
            Plotly Figure object
        """
        try:
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data.values,
                mode='lines',
                name=title,
                line=dict(color=color, width=2)
            ))
            
            fig.update_layout(
                title=title,
                height=400,
                template=self.chart_settings["theme"],
                hovermode='x'
            )
            
            fig.update_xaxes(title_text="Time", type='date')
            fig.update_yaxes(title_text="Value")
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating line chart: {str(e)}")
            return self._create_error_chart(str(e))
