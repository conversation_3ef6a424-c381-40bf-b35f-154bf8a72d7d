"""
Chart viewer for StreamTrade platform.
Integrates data management, indicators, and visualization.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import plotly.graph_objects as go

from ..data.data_manager import data_manager
from ..indicators.indicator_manager import IndicatorManager
from .plotly_charts import PlotlyCharts
from ..config.logging_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


class ChartViewer:
    """
    Main chart viewer that integrates data, indicators, and visualization.
    
    Features:
    - Load and display OHLCV data
    - Apply multiple technical indicators
    - Interactive chart with zoom/pan
    - Dynamic timeframe switching
    - Indicator management
    - Export capabilities
    """
    
    def __init__(self):
        self.indicator_manager = IndicatorManager()
        self.plotly_charts = PlotlyCharts()
        
        # Current chart state
        self.current_data = None
        self.current_pair = None
        self.current_timeframe = None
        self.current_indicators = {}
        
        logger.info("ChartViewer initialized")
    
    def load_data(
        self,
        pair: str,
        timeframe: str = "H1",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_candles: Optional[int] = None
    ) -> bool:
        """
        Load data for chart display.
        
        Args:
            pair: Currency pair
            timeframe: Timeframe
            start_date: Start date (optional)
            end_date: End date (optional)
            max_candles: Maximum number of candles
            
        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading data: {pair} {timeframe}")
            
            # Use default max_candles if not specified
            if max_candles is None:
                max_candles = settings.chart_settings["max_candles_display"]
            
            # Load data using data manager
            data = data_manager.get_data(
                pair=pair,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                max_candles=max_candles
            )
            
            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe}")
                return False
            
            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe
            
            # Clear previous indicator results
            self.current_indicators = {}
            
            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False
    
    def add_indicator(
        self,
        name: str,
        indicator_type: str,
        parameters: Optional[Dict[str, Any]] = None,
        display_name: Optional[str] = None
    ) -> bool:
        """
        Add an indicator to the chart.
        
        Args:
            name: Unique name for indicator instance
            indicator_type: Type of indicator
            parameters: Indicator parameters
            display_name: Display name for UI
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            success = self.indicator_manager.add_indicator(
                name=name,
                indicator_type=indicator_type,
                parameters=parameters,
                display_name=display_name
            )
            
            if success:
                logger.info(f"Added indicator: {name} ({indicator_type})")
                # Recalculate indicators if data is available
                if self.current_data is not None:
                    self._calculate_indicators()
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding indicator: {str(e)}")
            return False
    
    def remove_indicator(self, name: str) -> bool:
        """Remove an indicator from the chart."""
        try:
            success = self.indicator_manager.remove_indicator(name)
            
            if success:
                # Remove from current indicators
                if name in self.current_indicators:
                    del self.current_indicators[name]
                
                logger.info(f"Removed indicator: {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error removing indicator: {str(e)}")
            return False
    
    def update_indicator_parameters(
        self,
        name: str,
        parameters: Dict[str, Any]
    ) -> bool:
        """Update parameters for an existing indicator."""
        try:
            success = self.indicator_manager.update_indicator_parameters(name, parameters)
            
            if success and self.current_data is not None:
                # Recalculate indicators
                self._calculate_indicators()
                logger.info(f"Updated indicator parameters: {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating indicator parameters: {str(e)}")
            return False
    
    def toggle_indicator(self, name: str, enabled: bool) -> bool:
        """Enable or disable an indicator."""
        try:
            if enabled:
                success = self.indicator_manager.enable_indicator(name)
            else:
                success = self.indicator_manager.disable_indicator(name)
            
            if success and self.current_data is not None:
                self._calculate_indicators()
            
            return success
            
        except Exception as e:
            logger.error(f"Error toggling indicator: {str(e)}")
            return False
    
    def _calculate_indicators(self):
        """Calculate all indicators for current data."""
        try:
            if self.current_data is None:
                return
            
            self.current_indicators = self.indicator_manager.calculate_all(self.current_data)
            logger.debug(f"Calculated {len(self.current_indicators)} indicators")
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
    
    def create_chart(
        self,
        title: Optional[str] = None,
        height: Optional[int] = None,
        width: Optional[int] = None,
        remove_gaps: bool = True,
        chart_style: str = "Candlestick",
        show_vertical_line: bool = True,
        show_horizontal_line: bool = True
    ) -> go.Figure:
        """
        Create chart with current data and indicators.

        Args:
            title: Chart title (auto-generated if None)
            height: Chart height
            width: Chart width
            remove_gaps: Whether to remove weekend gaps
            chart_style: Chart style (Candlestick, OHLC Bars, Line)
            show_vertical_line: Show vertical crosshair line
            show_horizontal_line: Show horizontal crosshair line

        Returns:
            Plotly Figure object
        """
        try:
            if self.current_data is None:
                logger.warning("No data loaded for chart creation")
                return self._create_empty_chart()
            
            # Generate title if not provided
            if title is None:
                title = f"{self.current_pair} - {self.current_timeframe}"
                if len(self.current_data) > 0:
                    start_date = self.current_data.index[0].strftime("%Y-%m-%d")
                    end_date = self.current_data.index[-1].strftime("%Y-%m-%d")
                    title += f" ({start_date} to {end_date})"
            
            # Calculate indicators if not already done
            if not self.current_indicators and len(self.indicator_manager.indicators) > 0:
                self._calculate_indicators()
            
            # Create chart
            fig = self.plotly_charts.create_candlestick_chart(
                data=self.current_data,
                indicators=self.current_indicators,
                title=title,
                height=height,
                width=width,
                remove_gaps=remove_gaps,
                chart_style=chart_style,
                show_vertical_line=show_vertical_line,
                show_horizontal_line=show_horizontal_line
            )
            
            logger.debug(f"Created chart with {len(self.current_data)} candles and {len(self.current_indicators)} indicators")
            return fig
            
        except Exception as e:
            logger.error(f"Error creating chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def refresh_chart(self) -> go.Figure:
        """Refresh chart with current settings."""
        try:
            if self.current_pair and self.current_timeframe:
                # Reload data
                self.load_data(self.current_pair, self.current_timeframe)
                
                # Create new chart
                return self.create_chart()
            else:
                logger.warning("No current data to refresh")
                return self._create_empty_chart()
                
        except Exception as e:
            logger.error(f"Error refreshing chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def change_timeframe(self, new_timeframe: str) -> go.Figure:
        """
        Change timeframe and update chart.
        
        Args:
            new_timeframe: New timeframe to display
            
        Returns:
            Updated chart figure
        """
        try:
            if not self.current_pair:
                logger.warning("No pair loaded for timeframe change")
                return self._create_empty_chart()
            
            # Load data with new timeframe
            success = self.load_data(self.current_pair, new_timeframe)
            
            if success:
                return self.create_chart()
            else:
                logger.error(f"Failed to load data for timeframe {new_timeframe}")
                return self._create_error_chart(f"Failed to load {new_timeframe} data")
                
        except Exception as e:
            logger.error(f"Error changing timeframe: {str(e)}")
            return self._create_error_chart(str(e))
    
    def get_chart_info(self) -> Dict[str, Any]:
        """Get information about current chart state."""
        return {
            'pair': self.current_pair,
            'timeframe': self.current_timeframe,
            'data_points': len(self.current_data) if self.current_data is not None else 0,
            'date_range': {
                'start': self.current_data.index[0] if self.current_data is not None and len(self.current_data) > 0 else None,
                'end': self.current_data.index[-1] if self.current_data is not None and len(self.current_data) > 0 else None
            },
            'indicators': list(self.current_indicators.keys()),
            'indicator_count': len(self.current_indicators)
        }
    
    def get_available_pairs(self) -> List[str]:
        """Get list of available currency pairs."""
        return data_manager.get_available_pairs()
    
    def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes."""
        return data_manager.get_available_timeframes()
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get list of available indicators."""
        return self.indicator_manager.get_available_indicators()
    
    def get_indicators_by_category(self) -> Dict[str, List[str]]:
        """Get indicators grouped by category."""
        return self.indicator_manager.get_indicators_by_category()
    
    def get_indicator_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific indicator."""
        return self.indicator_manager.get_indicator_info(name)
    
    def export_indicator_config(self) -> str:
        """Export current indicator configuration."""
        return self.indicator_manager.export_configuration()
    
    def import_indicator_config(self, config_json: str) -> bool:
        """Import indicator configuration."""
        success = self.indicator_manager.import_configuration(config_json)
        
        if success and self.current_data is not None:
            self._calculate_indicators()
        
        return success
    
    def _create_empty_chart(self) -> go.Figure:
        """Create empty chart placeholder."""
        fig = go.Figure()
        
        fig.add_annotation(
            text="No data loaded<br>Please select a currency pair and timeframe",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        
        fig.update_layout(
            title="StreamTrade Chart",
            height=600,
            width=1000,
            template=settings.chart_settings["theme"]
        )
        
        return fig
    
    def _create_error_chart(self, error_message: str) -> go.Figure:
        """Create error chart."""
        return self.plotly_charts._create_error_chart(error_message)
