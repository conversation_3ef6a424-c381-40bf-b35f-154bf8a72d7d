"""
Indicator panel component for StreamTrade GUI.
"""

import streamlit as st
import json
from typing import Dict, Any, List, Optional

from ...config.logging_config import get_logger
from ...visualization.chart_viewer import ChartViewer

logger = get_logger(__name__)


class IndicatorPanel:
    """
    Indicator management panel for adding, configuring, and managing technical indicators.
    """
    
    def __init__(self, chart_viewer: ChartViewer):
        self.chart_viewer = chart_viewer
    
    def render(self):
        """Render the complete indicator panel."""
        st.subheader("📊 Technical Indicators")
        
        # Check if data is loaded
        if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
            st.info("Please load data first to use indicators.")
            return
        
        # Create tabs for different indicator functions
        tab1, tab2, tab3 = st.tabs(["➕ Add Indicators", "⚙️ Manage Indicators", "💾 Presets"])
        
        with tab1:
            self._render_add_indicator()
        
        with tab2:
            self._render_manage_indicators()
        
        with tab3:
            self._render_presets()
    
    def _render_add_indicator(self):
        """Render the add indicator interface."""
        st.write("**Add New Indicator**")
        
        # Get available indicators by category
        indicators_by_category = self.chart_viewer.get_indicators_by_category()
        
        if not indicators_by_category:
            st.error("No indicators available")
            return
        
        # Category selection
        selected_category = st.selectbox(
            "Category",
            options=list(indicators_by_category.keys()),
            help="Select indicator category"
        )
        
        # Indicator selection within category
        available_indicators = indicators_by_category[selected_category]
        selected_indicator = st.selectbox(
            "Indicator",
            options=available_indicators,
            help="Select specific indicator"
        )
        
        # Get indicator information
        try:
            from ...indicators.technical_indicators import TechnicalIndicators
            indicator_instance = TechnicalIndicators.get_indicator(selected_indicator)
            indicator_info = indicator_instance.get_info()
            
            # Display indicator description
            st.info(f"**{indicator_info['name']}**: {indicator_info['description']}")
            
            # Instance name input
            instance_name = st.text_input(
                "Instance Name",
                value=f"{selected_indicator}_1",
                help="Unique name for this indicator instance"
            )
            
            # Parameter configuration
            st.write("**Parameters**")
            parameters = {}
            
            for param_name, param_info in indicator_info['parameters'].items():
                param_type = param_info['type']
                default_value = param_info['default']
                min_value = param_info.get('min_value')
                max_value = param_info.get('max_value')
                description = param_info.get('description', '')
                options = param_info.get('options')
                
                if param_type == 'int':
                    parameters[param_name] = st.number_input(
                        param_name.replace('_', ' ').title(),
                        min_value=min_value,
                        max_value=max_value,
                        value=default_value,
                        step=1,
                        help=description
                    )
                elif param_type == 'float':
                    parameters[param_name] = st.number_input(
                        param_name.replace('_', ' ').title(),
                        min_value=min_value,
                        max_value=max_value,
                        value=float(default_value),
                        step=0.1,
                        help=description
                    )
                elif param_type == 'bool':
                    parameters[param_name] = st.checkbox(
                        param_name.replace('_', ' ').title(),
                        value=default_value,
                        help=description
                    )
                elif options:
                    parameters[param_name] = st.selectbox(
                        param_name.replace('_', ' ').title(),
                        options=options,
                        index=options.index(default_value) if default_value in options else 0,
                        help=description
                    )
            
            # Add indicator button
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("➕ Add Indicator", type="primary", use_container_width=True):
                    success = self.chart_viewer.add_indicator(
                        name=instance_name,
                        indicator_type=selected_indicator,
                        parameters=parameters
                    )
                    
                    if success:
                        st.success(f"✅ Added {instance_name}")
                        # Update chart
                        fig = self.chart_viewer.create_chart()
                        if fig:
                            st.session_state.chart_figure = fig
                        st.rerun()
                    else:
                        st.error(f"❌ Failed to add {instance_name}")
            
            with col2:
                if st.button("🔄 Reset Parameters", use_container_width=True):
                    st.rerun()
        
        except Exception as e:
            logger.error(f"Error in add indicator: {str(e)}")
            st.error(f"Error: {str(e)}")
    
    def _render_manage_indicators(self):
        """Render the manage indicators interface."""
        st.write("**Current Indicators**")
        
        # Get current indicators
        indicators_info = self.chart_viewer.indicator_manager.get_all_indicators_info()
        
        if not indicators_info:
            st.info("No indicators added yet. Use the 'Add Indicators' tab to add some.")
            return
        
        # Display each indicator
        for name, info in indicators_info.items():
            with st.expander(f"📊 {info['display_name']} ({info['name']})", expanded=False):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.write(f"**Type:** {info['name']}")
                    st.write(f"**Category:** {info['category']}")
                    st.write(f"**Description:** {info['description']}")
                    
                    # Show current parameters
                    st.write("**Current Parameters:**")
                    for param, value in info['current_parameters'].items():
                        st.write(f"• {param}: {value}")
                
                with col2:
                    # Enable/Disable toggle
                    enabled = st.checkbox(
                        "Enabled",
                        value=info['enabled'],
                        key=f"enabled_{name}"
                    )
                    
                    if enabled != info['enabled']:
                        self.chart_viewer.toggle_indicator(name, enabled)
                        fig = self.chart_viewer.create_chart()
                        if fig:
                            st.session_state.chart_figure = fig
                        st.rerun()
                    
                    # Remove button
                    if st.button("🗑️ Remove", key=f"remove_{name}"):
                        success = self.chart_viewer.remove_indicator(name)
                        if success:
                            st.success(f"Removed {name}")
                            fig = self.chart_viewer.create_chart()
                            if fig:
                                st.session_state.chart_figure = fig
                            st.rerun()
                
                # Parameter editing
                if st.button(f"⚙️ Edit Parameters", key=f"edit_{name}"):
                    st.session_state[f"editing_{name}"] = True
                    st.rerun()
                
                # Parameter editing form
                if st.session_state.get(f"editing_{name}", False):
                    st.write("**Edit Parameters:**")
                    
                    new_parameters = {}
                    for param_name, param_info in info['parameters'].items():
                        current_value = info['current_parameters'].get(param_name, param_info['default'])
                        param_type = param_info['type']
                        
                        if param_type == 'int':
                            new_parameters[param_name] = st.number_input(
                                param_name.replace('_', ' ').title(),
                                min_value=param_info.get('min_value'),
                                max_value=param_info.get('max_value'),
                                value=int(current_value),
                                step=1,
                                key=f"edit_{name}_{param_name}"
                            )
                        elif param_type == 'float':
                            new_parameters[param_name] = st.number_input(
                                param_name.replace('_', ' ').title(),
                                min_value=param_info.get('min_value'),
                                max_value=param_info.get('max_value'),
                                value=float(current_value),
                                step=0.1,
                                key=f"edit_{name}_{param_name}"
                            )
                        elif param_type == 'bool':
                            new_parameters[param_name] = st.checkbox(
                                param_name.replace('_', ' ').title(),
                                value=bool(current_value),
                                key=f"edit_{name}_{param_name}"
                            )
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        if st.button("💾 Save Changes", key=f"save_{name}"):
                            success = self.chart_viewer.update_indicator_parameters(name, new_parameters)
                            if success:
                                st.success("Parameters updated!")
                                fig = self.chart_viewer.create_chart()
                                if fig:
                                    st.session_state.chart_figure = fig
                                st.session_state[f"editing_{name}"] = False
                                st.rerun()
                            else:
                                st.error("Failed to update parameters")
                    
                    with col2:
                        if st.button("❌ Cancel", key=f"cancel_{name}"):
                            st.session_state[f"editing_{name}"] = False
                            st.rerun()
        
        # Bulk actions
        if indicators_info:
            st.write("**Bulk Actions**")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("✅ Enable All"):
                    for name in indicators_info.keys():
                        self.chart_viewer.toggle_indicator(name, True)
                    fig = self.chart_viewer.create_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                    st.rerun()
            
            with col2:
                if st.button("❌ Disable All"):
                    for name in indicators_info.keys():
                        self.chart_viewer.toggle_indicator(name, False)
                    fig = self.chart_viewer.create_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                    st.rerun()
            
            with col3:
                if st.button("🗑️ Remove All"):
                    for name in list(indicators_info.keys()):
                        self.chart_viewer.remove_indicator(name)
                    fig = self.chart_viewer.create_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                    st.rerun()
    
    def _render_presets(self):
        """Render indicator presets interface."""
        st.write("**Indicator Presets**")
        
        # Predefined presets
        presets = {
            "Trend Following": {
                "SMA_20": {"indicator_type": "SMA", "parameters": {"period": 20}},
                "SMA_50": {"indicator_type": "SMA", "parameters": {"period": 50}},
                "EMA_12": {"indicator_type": "EMA", "parameters": {"period": 12}}
            },
            "Momentum Analysis": {
                "RSI_14": {"indicator_type": "RSI", "parameters": {"period": 14}},
                "MACD": {"indicator_type": "MACD", "parameters": {"fast": 12, "slow": 26, "signal": 9}},
                "Stochastic": {"indicator_type": "Stochastic", "parameters": {"k_period": 14, "d_period": 3}}
            },
            "Volatility Analysis": {
                "BollingerBands": {"indicator_type": "BollingerBands", "parameters": {"period": 20, "std": 2.0}},
                "ATR_14": {"indicator_type": "ATR", "parameters": {"period": 14}}
            }
        }
        
        # Preset selection
        selected_preset = st.selectbox(
            "Select Preset",
            options=list(presets.keys()),
            help="Choose a predefined indicator set"
        )
        
        # Show preset details
        preset_config = presets[selected_preset]
        st.write(f"**{selected_preset} includes:**")
        
        for name, config in preset_config.items():
            st.write(f"• {name}: {config['indicator_type']} with parameters {config['parameters']}")
        
        # Apply preset button
        if st.button(f"📥 Apply {selected_preset} Preset", type="primary"):
            success_count = 0
            
            for name, config in preset_config.items():
                success = self.chart_viewer.add_indicator(
                    name=name,
                    indicator_type=config['indicator_type'],
                    parameters=config['parameters']
                )
                if success:
                    success_count += 1
            
            if success_count > 0:
                st.success(f"✅ Applied {success_count}/{len(preset_config)} indicators")
                fig = self.chart_viewer.create_chart()
                if fig:
                    st.session_state.chart_figure = fig
                st.rerun()
            else:
                st.error("❌ Failed to apply preset")
        
        st.divider()
        
        # Export/Import configuration
        st.write("**Configuration Management**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Export Configuration**")
            if st.button("📤 Export Current Setup"):
                config_json = self.chart_viewer.export_indicator_config()
                st.download_button(
                    label="💾 Download Configuration",
                    data=config_json,
                    file_name="indicator_config.json",
                    mime="application/json"
                )
        
        with col2:
            st.write("**Import Configuration**")
            uploaded_file = st.file_uploader(
                "Choose configuration file",
                type=['json'],
                help="Upload a previously exported indicator configuration"
            )
            
            if uploaded_file is not None:
                try:
                    config_data = json.load(uploaded_file)
                    config_json = json.dumps(config_data)
                    
                    if st.button("📥 Import Configuration"):
                        success = self.chart_viewer.import_indicator_config(config_json)
                        if success:
                            st.success("✅ Configuration imported successfully!")
                            fig = self.chart_viewer.create_chart()
                            if fig:
                                st.session_state.chart_figure = fig
                            st.rerun()
                        else:
                            st.error("❌ Failed to import configuration")
                
                except Exception as e:
                    st.error(f"Invalid configuration file: {str(e)}")
    
    def render_indicator_summary(self):
        """Render a summary of current indicators."""
        summary = self.chart_viewer.indicator_manager.get_summary()
        
        if summary['total_indicators'] == 0:
            return
        
        st.subheader("📋 Indicator Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total", summary['total_indicators'])
        
        with col2:
            st.metric("Enabled", summary['enabled_indicators'])
        
        with col3:
            st.metric("Disabled", summary['disabled_indicators'])
        
        with col4:
            st.metric("Categories", len(summary['categories']))
        
        # Category breakdown
        if summary['categories']:
            st.write("**By Category:**")
            for category, count in summary['categories'].items():
                st.write(f"• {category.title()}: {count}")
