# StreamTrade Platform

Advanced Trading and Backtesting Platform built with Python.

## Overview

StreamTrade adalah platform trading yang komprehensif untuk analisis forex, backtesting, dan visualisasi. Platform ini dirancang untuk menangani data historis dalam jumlah besar dengan efisien dan menyediakan tools untuk analisis teknikal yang mendalam.

## Features (Phase 1 - Completed)

### ✅ Core Infrastructure
- **Configuration Management**: Centralized settings dengan YAML support
- **Logging System**: Comprehensive logging dengan rotation dan performance tracking
- **Memory Management**: Intelligent caching dan memory optimization
- **Error Handling**: Robust error handling dan validation

### ✅ Data Management
- **Data Loader**: Efficient loading untuk histdata.com CSV files
- **File Discovery**: Automatic scanning dan discovery dari data files
- **Data Validation**: Comprehensive OHLCV data validation
- **Lazy Loading**: Memory-efficient data loading dengan chunking

### ✅ Timeframe Conversion
- **Multi-Timeframe Support**: M1, M5, M15, M30, H1, H4, D1, W1, MN1
- **Efficient Resampling**: Optimized OHLCV aggregation
- **Data Integrity**: OHLC relationship validation dan correction
- **Caching**: Smart caching untuk conversion results

### ✅ Performance Optimization
- **Intelligent Caching**: Multi-level caching dengan automatic cleanup
- **Memory Monitoring**: Real-time memory usage tracking
- **Chunked Processing**: Handle large datasets efficiently
- **Thread Safety**: Thread-safe operations untuk concurrent access

## Project Structure

```
streamtrade/
├── config/                 # Configuration management
│   ├── __init__.py
│   ├── settings.py         # Main settings dan configuration
│   └── logging_config.py   # Logging configuration
├── core/                   # Core utilities
│   ├── __init__.py
│   └── utils.py           # Utility functions
├── data/                   # Data management
│   ├── __init__.py
│   ├── data_manager.py    # Main data manager
│   ├── data_loader.py     # CSV file loader
│   └── timeframe_converter.py  # Timeframe conversion
├── tests/                  # Unit tests
│   ├── __init__.py
│   └── test_data_manager.py
├── docs/                   # Documentation
├── requirements.txt        # Dependencies
├── main.py                # Testing dan development entry point
└── README.md              # This file
```

## Installation

1. **Clone atau setup project directory**:
```bash
cd /home/<USER>/Learn/python/notebooks/streamtrade
```

2. **Activate Python environment**:
```bash
source /home/<USER>/Learn/python/notebooks/venv/bin/activate
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

## Data Requirements

Platform ini menggunakan data dari histdata.com dengan struktur:

```
histdata/MT/M1/
├── eurusd/
│   ├── 2024/
│   │   └── DAT_MT_EURUSD_M1_2024.csv
│   └── 2025/
│       ├── 1/DAT_MT_EURUSD_M1_202501.csv
│       ├── 2/DAT_MT_EURUSD_M1_202502.csv
│       └── ...
├── gbpusd/
└── ...
```

**CSV Format**:
```
Date,Time,Open,High,Low,Close,Volume
2024.01.01,00:00,1.1050,1.1055,1.1048,1.1052,0
```

## Usage

### Basic Data Loading

```python
from streamtrade.data.data_manager import data_manager
from datetime import datetime, timedelta

# Load EURUSD H1 data
data = data_manager.get_data(
    pair="EURUSD",
    timeframe="H1",
    start_date=datetime.now() - timedelta(days=30),
    end_date=datetime.now(),
    max_candles=1000
)

print(f"Loaded {len(data)} candles")
print(data.head())
```

### Timeframe Conversion

```python
# Load M1 data dan convert ke multiple timeframes
m1_data = data_manager.get_data("EURUSD", "M1", max_candles=1440)

# Convert ke H1
h1_data = data_manager.get_data("EURUSD", "H1", max_candles=24)

# Convert ke D1
d1_data = data_manager.get_data("EURUSD", "D1", max_candles=1)
```

### Cache Management

```python
# Get cache statistics
cache_stats = data_manager.get_cache_stats()
print(f"Cache entries: {cache_stats['entries']}")
print(f"Cache size: {cache_stats['total_size_mb']:.1f} MB")

# Clear cache
data_manager.clear_cache()
```

## Testing

### Run Main Tests
```bash
cd streamtrade
python main.py
```

### Run Unit Tests
```bash
cd streamtrade
python -m pytest tests/ -v
```

### Manual Testing
```bash
cd streamtrade
python tests/test_data_manager.py
```

## Configuration

### Settings File
Configuration dapat di-customize melalui `config/settings.py`:

```python
# Data settings
data_settings = {
    "chunk_size": 100000,
    "cache_size_mb": 500,
    "supported_timeframes": ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"]
}

# Performance settings
performance_settings = {
    "max_memory_mb": 2048,
    "enable_multiprocessing": True,
    "cache_timeout_minutes": 30
}
```

### Logging Configuration
Logging dapat dikonfigurasi melalui `config/logging_config.py`:

```python
setup_logging(
    level="INFO",
    log_file="logs/streamtrade.log",
    console_output=True
)
```

## Performance

### Memory Usage
- **Efficient Caching**: Automatic memory management dengan LRU eviction
- **Lazy Loading**: Data dimuat hanya saat diperlukan
- **Chunked Processing**: Handle file besar tanpa memory overflow

### Speed Optimization
- **Smart Caching**: Cache results untuk repeated queries
- **Vectorized Operations**: Menggunakan pandas/numpy untuk speed
- **Parallel Processing**: Multi-threading untuk data processing

## Supported Data

### Currency Pairs
- Major pairs: EURUSD, GBPUSD, USDJPY, dll.
- Cross pairs: EURGBP, EURJPY, GBPJPY, dll.
- Commodities: XAUUSD (Gold), dll.
- Indices: SPXUSD, NSXUSD, dll.

### Timeframes
- **Minute**: M1, M5, M15, M30
- **Hourly**: H1, H4
- **Daily**: D1
- **Weekly**: W1
- **Monthly**: MN1

## Next Steps (Phase 2)

- [ ] Chart visualization dengan Plotly
- [ ] Technical indicators integration
- [ ] Streamlit GUI development
- [ ] Custom indicators framework
- [ ] Performance optimization
- [ ] Advanced caching strategies

## Troubleshooting

### Common Issues

1. **Memory Issues**:
   - Reduce `chunk_size` dalam settings
   - Decrease `max_memory_mb` limit
   - Clear cache regularly

2. **Data Loading Errors**:
   - Check histdata directory structure
   - Verify CSV file format
   - Check file permissions

3. **Performance Issues**:
   - Enable caching
   - Use appropriate `max_candles` limits
   - Monitor memory usage

### Logging
Check logs untuk detailed error information:
```bash
tail -f streamtrade/logs/streamtrade.log
```

## Contributing

1. Follow existing code structure
2. Add unit tests untuk new features
3. Update documentation
4. Test dengan real data
5. Monitor memory usage

## License

Internal project - StreamTrade Development Team
