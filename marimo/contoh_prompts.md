hi tolong bantu aku membuat strategi lengkap untuk library backtesting dan projecta marimo notebook dengan ketentuan seperti ini :

# Ketentuan / Rules
- ini adalah projecta marimo notebook, jadi tidak usah membuat fungsi __main__ atau yang lain yang tidak diperlukan
- copy isi template file marimo notebook dari file template.py dan buat file baru berdasarkan itu
- buat strategi pada cell nomor 06
- jangan rubah cell lain yang sudah ada terutama cell setup sampai dengan cell 05, ini adalah inisialisasi data.
- sedangkan cell 06a dan 06b adalah cell untuk menampilkan plot dan stats (boleh disesuaikan jika diperlukan)
- lihat cell setup untuk import library yang sudah ada, jangan import ulang yang sudah ada
- gunakan indikator dari ta-lib, tulip, pandas-ta atau yang lainnya untuk membuat strategi.
- buat file dengan penomoran pada folder strategies, misal 002.py jika 001 sudah ada
- tidak usah membuat fungsi untuk mengambil data, anggap data sudah ada yaitu pada variable df_backtesting yang ada pada cell nomor 05
- use context7
- cari di internet jika diperlukan
- ketika anda membuat dokumentasi, sesuaikan dengan nama file yang anda buat, misal 002.md

## Plot :
- plot semua indikator pada chart
- plot entry dan exit signal
- plot stop loss dan take profit

## Manajemen Resiko :
strategi ini dapat menyesuaikan
resiko entry = default 1%
rasio stop loss dan take profit = default 1:3
atr multiplier untuk stop loss = default 0.5


## Strategi entry :

- gunakan indikator ichimoku cloud
- gunakan indikator bollinger band

### Stop Loss :
stop loss diletakkan pada high candle kedua sebelumnya untuk sell, dan
stop loss ditentukan pada low candle kedua sebelumnya untuk buy

### BUY ketika :
ichimoku cloud = cloud bullish, kijun dan tenkan cross bullish juga
bollinger band = harga menyentuh garis bollinger band bawah dan close kembali di atas garis bollinger band bawah


### SELL ketika :
ichimoku cloud = cloud bearish, kijun dan tenkan cross bearish juga
bollinger band = harga menyentuh garis bollinger band atas dan close kembali di bawah garis bollinger band atas