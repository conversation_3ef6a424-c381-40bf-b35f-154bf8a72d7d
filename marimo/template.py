import marimo

__generated_with = "0.14.7"
app = marimo.App(width="medium")

with app.setup:
    import marimo as mo
    import pandas as pd
    from pathlib import Path
    import datetime
    from backtesting import Backtest, Strategy
    from backtesting.lib import crossover
    import talib


@app.cell(hide_code=True)
def _01():
    # 1. Tentukan path yang diinginkan
    desired_initial_path = Path("../historical_data/data")

    # 2. Periksa apakah path yang diinginkan ada dan merupakan direktori
    if desired_initial_path.is_dir():
        # Jika ya, gunakan path tersebut
        actual_initial_path = desired_initial_path
        mo.md(f"Menggunakan direktori awal yang ditentukan: `{actual_initial_path.resolve()}`")
    else:
        # Jika tidak, berikan pesan peringatan dan gunakan direktori saat ini sebagai fallback
        actual_initial_path = Path(".") # Menggunakan direktori saat ini sebagai fallback
        mo.md(
            f"**Peringatan:** Direktori `{desired_initial_path.resolve()}` tidak ditemukan atau bukan direktori. "
            f"Menggunakan direktori saat ini: `{actual_initial_path.resolve()}` sebagai gantinya."
        )

    # 3. Membuat elemen UI file_browser dengan path yang sudah diverifikasi
    file_browser = mo.ui.file_browser(
        initial_path=actual_initial_path, # Gunakan path yang sudah diverifikasi
        multiple=False, # Hanya izinkan pemilihan satu file
        label="Pilih File Data Anda" # Label untuk elemen UI
    )
    return (file_browser,)


@app.cell(hide_code=True)
def _01a(file_browser):
    file_browser
    return


@app.cell(hide_code=True)
def _02(file_browser):
    # Pastikan file_browser sudah didefinisikan dan file sudah dipilih
    if not file_browser.path:
        mo.stop(mo.md("### Silakan pilih file CSV dari penjelajah file di atas."))

    # --- PERBAIKAN UNTUK AttributeError DIMULAI DI SINI ---
    # Dapatkan nilai 'path' dari file_browser.
    # Kita akan memeriksa apakah itu fungsi atau atribut langsung.
    _raw_path_value = file_browser.path

    # Jika _raw_path_value adalah fungsi, panggil untuk mendapatkan Path object.
    if callable(_raw_path_value):
        selected_file_path = _raw_path_value()
        mo.md(f"**Info:** `file_browser.path` terdeteksi sebagai fungsi dan dipanggil.")
        # Pastikan hasil panggilannya adalah Path object
        if not isinstance(selected_file_path, Path):
            mo.stop(mo.md(f"### Kesalahan: `file_browser.path()` tidak mengembalikan Path yang valid. Tipe: {type(selected_file_path)}"))
    else:
        # Jika bukan fungsi, asumsikan itu sudah Path object atau None
        selected_file_path = _raw_path_value

    # Pastikan selected_file_path adalah Path object dan bukan None
    if not isinstance(selected_file_path, Path):
        mo.stop(mo.md(f"### Kesalahan: Path file yang dipilih tidak valid atau kosong. Tipe: {type(selected_file_path)}"))

    selected_file_name = file_browser.name # Atribut .name seharusnya tetap berfungsi normal
    # --- PERBAIKAN UNTUK AttributeError BERAKHIR DI SINI ---


    # 1. Validasi ekstensi file
    if selected_file_path.suffix.lower() != ".csv":
        mo.stop(mo.md(f"### File yang dipilih `{selected_file_name}` bukan file CSV. Silakan pilih file CSV."))

    mo.md(f"### Memuat dan Memvalidasi File: `{selected_file_name}`")

    try:
        # Memuat data CSV tanpa header (karena tidak ada header di file histdata.com)
        # Nama kolom akan kita berikan secara manual
        df_histdata = pd.read_csv(
            selected_file_path,
            header=None, # Tidak ada header
            names=[
                "Date Stamp",
                "Time Stamp",
                "Open",
                "High",
                "Low",
                "Close",
                "Volume",
            ],
            dtype={ # Tentukan tipe data untuk efisiensi dan akurasi
                "Open": float,
                "High": float,
                "Low": float,
                "Close": float,
                "Volume": int,
            },
        )

        # 2. Validasi kolom dan format data
        required_columns = [
            "Date Stamp",
            "Time Stamp",
            "Open",
            "High",
            "Low",
            "Close",
            "Volume",
        ]
        if not all(col in df_histdata.columns for col in required_columns):
            mo.stop(
                mo.md(
                    f"### Format file `{selected_file_name}` tidak sesuai spesifikasi."
                    f"Kolom yang diharapkan: {', '.join(required_columns)}"
                )
            )

        # Menggabungkan Date Stamp dan Time Stamp menjadi kolom DateTime
        # Format: YYYY.MM.DD HH:MM
        df_histdata["DateTime"] = pd.to_datetime(
            df_histdata["Date Stamp"] + " " + df_histdata["Time Stamp"],
            format="%Y.%m.%d %H:%M",
            errors="coerce", # Ubah nilai yang tidak valid menjadi NaT (Not a Time)
        )

        # Periksa apakah ada nilai NaT setelah konversi, menandakan format yang tidak sesuai
        if df_histdata["DateTime"].isnull().any():
            mo.stop(
                mo.md(
                    f"### Format tanggal atau waktu di file `{selected_file_name}` tidak sesuai spesifikasi (YYYY.MM.DD HH:MM)."
                    "Silakan periksa data Anda."
                )
            )

        # Hapus kolom asli Date Stamp dan Time Stamp
        df_histdata = df_histdata.drop(columns=["Date Stamp", "Time Stamp"])

        # Urutkan berdasarkan DateTime untuk memastikan urutan yang benar
        df_histdata = df_histdata.sort_values(by="DateTime").reset_index(drop=True)

        # 3. Cek tanggal/date-time awal dan akhir
        min_date = df_histdata["DateTime"].min().date() # Ambil hanya tanggalnya
        max_date = df_histdata["DateTime"].max().date() # Ambil hanya tanggalnya

        # --- BARU: Ekstraksi Nama Pair ---
        # Contoh nama file: DAT_MT_XAUUSD_M1_2023.csv
        # Kita ingin mengambil 'XAUUSD'
        try:
            # Hapus ekstensi .csv
            nama_file = file_browser.name(index=0)
            name_without_ext = nama_file.replace(".csv", "")
            # Pisahkan string berdasarkan underscore
            parts = name_without_ext.split("_")

            # --- DEBUGGING ADDITION ---
            mo.md(f"**DEBUG (Pair Extraction):**")
            mo.md(f"- `selected_file_name`: `{selected_file_name}`")
            mo.md(f"- `name_without_ext`: `{name_without_ext}`")
            mo.md(f"- `parts` array: `{parts}` (Length: {len(parts)})")
            # --- END DEBUGGING ADDITION ---

            # Asumsi nama pair ada di indeks ke-2 setelah DAT_MT_
            # (DAT, MT, XAUUSD, M1, 2023)
            if len(parts) >= 3:
                pair_name = parts[2]
            else:
                pair_name = "UNKNOWN_PAIR" # Fallback jika format tidak sesuai
                mo.md(f"**Peringatan:** Tidak dapat mengekstrak nama pair dari `{selected_file_name}`. Menggunakan `{pair_name}`. Bagian: `{parts}`") # <-- Perbaikan di sini
        except Exception as e:
            pair_name = "UNKNOWN_PAIR"
            mo.md(f"**Peringatan:** Terjadi kesalahan saat mengekstrak nama pair: {e}. Menggunakan `{pair_name}`.")
        # --- AKHIR DARI EKSTRAKSI NAMA PAIR ---

        mo.md(
            f"File `{selected_file_name}` berhasil dimuat dan divalidasi."
            f"Data untuk **{pair_name}** tersedia dari **{min_date}** hingga **{max_date}**."
        )

    except pd.errors.EmptyDataError:
        mo.stop(mo.md(f"### File `{selected_file_name}` kosong atau tidak memiliki data."))
    except Exception as e:
        mo.stop(mo.md(f"### Gagal memuat atau memvalidasi file `{selected_file_name}`: {e}"))

    # Variabel yang akan digunakan di sel berikutnyaa
    # df_histdata, min_date, max_date, pair_name
    return df_histdata, max_date, min_date, pair_name


@app.cell(hide_code=True)
def _03(df_histdata, max_date, min_date, pair_name):
    # Pastikan df_histdata, min_date, max_date, dan pair_name sudah didefinisikan di sel sebelumnya
    # Jika sel sebelumnya berhenti, variabel ini tidak akan ada, jadi kita perlu cek
    if "df_histdata" not in locals():
        mo.stop(mo.md("### Data belum dimuat atau divalidasi. Silakan pilih file CSV yang valid."))

    # UI untuk memilih tanggal mulai dan tanggal akhir
    start_date_ui = mo.ui.date(
        value=min_date, # Default ke tanggal minimum dari data
        label="Tanggal Mulai",
        full_width=False,
    )

    end_date_ui = mo.ui.date(
        value=max_date, # Default ke tanggal maksimum dari data
        label="Tanggal Akhir",
        full_width=False,
    )

    # UI untuk memilih berapa rows yang ingin ditampilkan
    num_rows_ui = mo.ui.number(
        value=50, # Default 10 baris
        start=1, # Minimal 1 baris
        stop=len(df_histdata), # Maksimal jumlah baris dalam data
        label="Jumlah Baris Tampil",
        full_width=False,
    )

    # UI untuk memilih urutan (ASC/DESC)
    sort_order_ui = mo.ui.radio(
        options={"Ascending": "asc", "Descending": "desc"},
        value="Ascending", # Gunakan kunci "Ascending"
        label="Urutan Tampilan",
    )

    # --- BARU: UI untuk mengaktifkan/menonaktifkan mode debug ---
    debug_toggle = mo.ui.checkbox(
        label="Aktifkan Mode Debug",
        value=False, # Default nonaktif
    )

    # Tombol untuk menampilkan data
    display_button = mo.ui.run_button(label="Tampilkan Data")

    heading = mo.md(f"PAIR : {pair_name}")

    # Tampilkan semua elemen UI dalam tata letak yang rapi
    mo.vstack(
        [
            mo.hstack([heading]),
            mo.hstack([start_date_ui, end_date_ui]),
            mo.hstack([num_rows_ui, sort_order_ui]),
            mo.hstack([debug_toggle, display_button]), # Letakkan debug_toggle di samping tombol
        ]
    )
    return (
        debug_toggle,
        display_button,
        end_date_ui,
        num_rows_ui,
        sort_order_ui,
        start_date_ui,
    )


@app.cell(hide_code=True)
def _04(
    debug_toggle,
    df_histdata,
    display_button,
    end_date_ui,
    num_rows_ui,
    sort_order_ui,
    start_date_ui,
):
    # Pastikan df_histdata sudah didefinisikan
    if "df_histdata" not in locals():
        mo.stop(mo.md("### Data belum dimuat. Silakan pilih file CSV yang valid."))

    # Hentikan eksekusi sel ini jika tombol belum diklik.
    mo.stop(not display_button.value, mo.md("### Klik 'Tampilkan Data' untuk melihat pratinjau."))

    # --- MODIFIKASI: Gunakan debug_toggle.value untuk mengontrol output debug ---
    if debug_toggle.value:
        print("DEBUG: Sel eksekusi dimulai.")
        print(f"DEBUG: display_button.value sebelum mo.stop: {display_button.value}")

    # Jika kode mencapai sini, berarti tombol sudah diklik
    if debug_toggle.value:
        print(f"DEBUG: display_button.value setelah mo.stop (seharusnya True/non-None): {display_button.value}")
        mo.md("--- **DEBUGGING OUTPUT** ---")
        mo.md(f"**Tombol 'Tampilkan Data' telah diklik.**")

    # Ambil nilai dari UI
    selected_start_date = start_date_ui.value
    selected_end_date = end_date_ui.value
    selected_num_rows = num_rows_ui.value
    selected_sort_order = sort_order_ui.value

    if debug_toggle.value:
        mo.md(f"**Pilihan Filter:**")
        mo.md(f"- Tanggal Mulai: `{selected_start_date}`")
        mo.md(f"- Tanggal Akhir: `{selected_end_date}`")
        mo.md(f"- Jumlah Baris Tampil: `{selected_num_rows}`")
        mo.md(f"- Urutan Tampilan: `{selected_sort_order}`")

        print(f"DEBUG: selected_start_date: {selected_start_date}, type: {type(selected_start_date)}")
        print(f"DEBUG: selected_end_date: {selected_end_date}, type: {type(selected_end_date)}")
        print(f"DEBUG: selected_num_rows: {selected_num_rows}, type: {type(selected_num_rows)}")
        print(f"DEBUG: selected_sort_order: {selected_sort_order}, type: {type(selected_sort_order)}")
        print(f"DEBUG: df_histdata columns: {df_histdata.columns.tolist()}")
        print(f"DEBUG: df_histdata DateTime min: {df_histdata['DateTime'].min()}, max: {df_histdata['DateTime'].max()}")
        print(f"DEBUG: df_histdata head:\n{df_histdata.head().to_string()}")

    # Filter data berdasarkan rentang tanggal
    filtered_df = df_histdata[
        (df_histdata["DateTime"].dt.date >= selected_start_date)
        & (df_histdata["DateTime"].dt.date <= selected_end_date)
    ].copy()

    if debug_toggle.value:
        mo.md(f"**Jumlah baris setelah filter tanggal:** `{len(filtered_df)}`")
        print(f"DEBUG: Jumlah baris setelah filter tanggal: {len(filtered_df)}")

    # Inisialisasi variabel untuk menampung elemen output akhir
    final_output_element = None

    if filtered_df.empty:
        final_output_element = mo.md("### Tidak ada data yang ditemukan untuk rentang tanggal yang dipilih.")
        if debug_toggle.value:
            print("DEBUG: filtered_df is empty.")
    else:
        # Terapkan pengurutan
        if selected_sort_order == "asc":
            final_df = filtered_df.sort_values(by="DateTime", ascending=True)
        else: # desc
            final_df = filtered_df.sort_values(by="DateTime", ascending=False)

        # Ambil sejumlah baris yang diminta
        final_df = final_df.head(selected_num_rows)

        if debug_toggle.value:
            mo.md(f"**Jumlah baris akhir yang akan ditampilkan:** `{len(final_df)}`")
            print(f"DEBUG: Jumlah baris akhir yang akan ditampilkan: {len(final_df)}")
            print("DEBUG: mo.ui.dataframe should have been displayed.")

        mo.md(f"### Pratinjau Data ({len(final_df)} baris):")
        #final_output_element = mo.ui.dataframe(final_df, page_size=10)
        final_output_element = mo.ui.table(final_df, page_size=10, label="Format konversi awal")

    if debug_toggle.value:
        mo.md("--- **END DEBUGGING OUTPUT** ---")
        print("DEBUG: Sel eksekusi selesai.")

    final_output_element
    return (
        selected_end_date,
        selected_num_rows,
        selected_sort_order,
        selected_start_date,
    )


@app.cell(hide_code=True)
def _05(
    df_histdata,
    display_button,
    selected_end_date,
    selected_num_rows,
    selected_sort_order,
    selected_start_date,
):

    # Pastikan df_histdata sudah didefinisikan
    if "df_histdata" not in locals():
        mo.stop(mo.md("### Data belum dimuat. Silakan pilih file CSV yang valid."))

    # Hentikan eksekusi sel ini jika tombol belum diklik.
    mo.stop(not display_button.value, mo.md("### Klik 'Tampilkan Data' untuk melihat pratinjau."))

    # --- BARIS-BARIS INI DIHAPUS KARENA SUDAH DIDEFINISIKAN DI SEL SEBELUMNYA (_04) ---
    # selected_start_date = start_date_ui.value
    # selected_end_date = end_date_ui.value
    # selected_num_rows = num_rows_ui.value
    # selected_sort_order = sort_order_ui.value
    # --- AKHIR DARI BARIS YANG DIHAPUS ---

    # --- Bagian Konversi Data untuk Backtesting ---
    # 1. Buat salinan DataFrame agar tidak memodifikasi df_histdata asli
    df_backtesting = df_histdata.copy()

    # 2. Atur kolom 'DateTime' sebagai indeks
    df_backtesting = df_backtesting.set_index("DateTime")

    # 3. Ubah nama kolom data (Open, High, Low, Close, Volume) menjadi huruf kecil
    # Ini adalah perbaikan dari baris yang salah sebelumnya
    # df_backtesting.columns = df_backtesting.columns.str.lower() # <--- BARIS INI DIHAPUS/DIKOMENTARI

    # --- Bagian Pemfilteran dan Tampilan ---
    # Filter data berdasarkan rentang tanggal menggunakan indeks DataFrame
    filtered_dfx = df_backtesting[
        (df_backtesting.index.date >= selected_start_date) # selected_start_date sudah tersedia dari _04
        & (df_backtesting.index.date <= selected_end_date)   # selected_end_date sudah tersedia dari _04
    ].copy()

    # Inisialisasi variabel untuk menampung elemen output akhir
    final_output_element_backtesting = None

    if filtered_dfx.empty:
        final_output_element_backtesting = mo.md("### Tidak ada data yang ditemukan untuk rentang tanggal yang dipilih pada data backtesting.")
    else:
        # Terapkan pengurutan berdasarkan indeks (DateTime)
        if selected_sort_order == "asc": # selected_sort_order sudah tersedia dari _04
            final_dfx = filtered_dfx.sort_index(ascending=True)
        else: # desc
            final_dfx = filtered_dfx.sort_index(ascending=False)

        # Ambil sejumlah baris yang diminta
        final_dfx = final_dfx.head(selected_num_rows) # selected_num_rows sudah tersedia dari _04

        mo.md(f"### Pratinjau Data untuk Backtesting ({len(final_dfx)} baris):")
        #final_output_element_backtesting = mo.ui.dataframe(final_dfx, page_size=10)
        final_output_element_backtesting = mo.ui.table(final_dfx, page_size=10, label="Format untuk Lib : backtesting.py")

    final_output_element_backtesting
    return (df_backtesting,)


@app.cell(hide_code=True)
def _06(df_backtesting):
    def SMA(values, n):
        """
        Return simple moving average of `values`, at
        each step taking into account `n` previous values.
        """
        return pd.Series(values).rolling(n).mean()

    class SmaCross(Strategy):
        # Define the two MA lags as *class variables*
        # for later optimization
        n1 = 50
        n2 = 200

        def init(self):
            # Precompute the two moving averages
            self.sma1 = self.I(SMA, self.data.Close, self.n1)
            self.sma2 = self.I(SMA, self.data.Close, self.n2)

        def next(self):
            # If sma1 crosses above sma2, close any existing
            # short trades, and buy the asset
            if crossover(self.sma1, self.sma2):
                self.position.close()
                self.buy()

            # Else, if sma1 crosses below sma2, close any existing
            # long trades, and sell the asset
            elif crossover(self.sma2, self.sma1):
                self.position.close()
                self.sell()

    bt = Backtest(df_backtesting, SmaCross, cash=10_000, commission=.002)
    stats = bt.run()
    return bt, stats


@app.cell
def _06a(bt):
    bt.plot()
    return


@app.cell
def _06b(stats):
    mo.md(
        f"""
    ```text
    {stats}
    ```
    """
    )
    return


if __name__ == "__main__":
    app.run()
